{"version": 3, "file": "PromptCachingBetaMessageStream.mjs", "sourceRoot": "", "sources": ["../src/lib/PromptCachingBetaMessageStream.ts"], "names": [], "mappings": ";;;;;;;;;;;;OACO,EAAE,cAAc,EAAE,iBAAiB,EAAE,MAAM,yBAAyB;OAWpE,EAAE,MAAM,EAAE,MAAM,6BAA6B;OAC7C,EAAE,YAAY,EAAE;AAqBvB,MAAM,iBAAiB,GAAG,YAAY,CAAC;AAEvC,MAAM,OAAO,8BAA8B;IAwBzC;;QAvBA,aAAQ,GAAoC,EAAE,CAAC;QAC/C,qBAAgB,GAA+B,EAAE,CAAC;QAClD,yEAA8D;QAE9D,eAAU,GAAoB,IAAI,eAAe,EAAE,CAAC;QAEpD,mEAAiC;QACjC,kEAAuC,GAAG,EAAE,GAAE,CAAC,EAAC;QAChD,iEAA2D,GAAG,EAAE,GAAE,CAAC,EAAC;QAEpE,6DAA2B;QAC3B,4DAAiC,GAAG,EAAE,GAAE,CAAC,EAAC;QAC1C,2DAAqD,GAAG,EAAE,GAAE,CAAC,EAAC;QAE9D,oDAEI,EAAE,EAAC;QAEP,gDAAS,KAAK,EAAC;QACf,kDAAW,KAAK,EAAC;QACjB,kDAAW,KAAK,EAAC;QACjB,iEAA0B,KAAK,EAAC;QAsPhC,sDAAe,CAAC,KAAc,EAAE,EAAE;YAChC,uBAAA,IAAI,2CAAY,IAAI,MAAA,CAAC;YACrB,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE;gBACzD,KAAK,GAAG,IAAI,iBAAiB,EAAE,CAAC;aACjC;YACD,IAAI,KAAK,YAAY,iBAAiB,EAAE;gBACtC,uBAAA,IAAI,2CAAY,IAAI,MAAA,CAAC;gBACrB,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;aACnC;YACD,IAAI,KAAK,YAAY,cAAc,EAAE;gBACnC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;aACnC;YACD,IAAI,KAAK,YAAY,KAAK,EAAE;gBAC1B,MAAM,cAAc,GAAmB,IAAI,cAAc,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBACzE,aAAa;gBACb,cAAc,CAAC,KAAK,GAAG,KAAK,CAAC;gBAC7B,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;aAC5C;YACD,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAChE,CAAC,EAAC;QAtQA,uBAAA,IAAI,oDAAqB,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC7D,uBAAA,IAAI,2DAA4B,OAAO,MAAA,CAAC;YACxC,uBAAA,IAAI,0DAA2B,MAAM,MAAA,CAAC;QACxC,CAAC,CAAC,MAAA,CAAC;QAEH,uBAAA,IAAI,8CAAe,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACvD,uBAAA,IAAI,qDAAsB,OAAO,MAAA,CAAC;YAClC,uBAAA,IAAI,oDAAqB,MAAM,MAAA,CAAC;QAClC,CAAC,CAAC,MAAA,CAAC;QAEH,6DAA6D;QAC7D,4DAA4D;QAC5D,6DAA6D;QAC7D,gCAAgC;QAChC,uBAAA,IAAI,wDAAkB,CAAC,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QACvC,uBAAA,IAAI,kDAAY,CAAC,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IACnC,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,kBAAkB,CAAC,MAAsB;QAC9C,MAAM,MAAM,GAAG,IAAI,8BAA8B,EAAE,CAAC;QACpD,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC;QACtD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,aAAa,CAClB,QAAkB,EAClB,MAA+B,EAC/B,OAA6B;QAE7B,MAAM,MAAM,GAAG,IAAI,8BAA8B,EAAE,CAAC;QACpD,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE;YACrC,MAAM,CAAC,iCAAiC,CAAC,OAAO,CAAC,CAAC;SACnD;QACD,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CACf,MAAM,CAAC,+BAA+B,CACpC,QAAQ,EACR,EAAE,GAAG,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAC3B,EAAE,GAAG,OAAO,EAAE,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,OAAO,EAAE,2BAA2B,EAAE,QAAQ,EAAE,EAAE,CACxF,CACF,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;IAES,IAAI,CAAC,QAA4B;QACzC,QAAQ,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;YACnB,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACpB,CAAC,EAAE,uBAAA,IAAI,mDAAa,CAAC,CAAC;IACxB,CAAC;IAES,iCAAiC,CAAC,OAAsC;QAChF,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;IAES,4BAA4B,CAAC,OAAiC,EAAE,IAAI,GAAG,IAAI;QACnF,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;SAChC;IACH,CAAC;IAES,KAAK,CAAC,+BAA+B,CAC7C,QAAkB,EAClB,MAA2B,EAC3B,OAA6B;QAE7B,MAAM,MAAM,GAAG,OAAO,EAAE,MAAM,CAAC;QAC/B,IAAI,MAAM,EAAE;YACV,IAAI,MAAM,CAAC,OAAO;gBAAE,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YAC5C,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC;SACjE;QACD,uBAAA,IAAI,+FAAc,MAAlB,IAAI,CAAgB,CAAC;QACrB,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,MAAM,CAClC,EAAE,GAAG,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAC3B,EAAE,GAAG,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAC/C,CAAC;QACF,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE;YAChC,uBAAA,IAAI,iGAAgB,MAApB,IAAI,EAAiB,KAAK,CAAC,CAAC;SAC7B;QACD,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE;YACrC,MAAM,IAAI,iBAAiB,EAAE,CAAC;SAC/B;QACD,uBAAA,IAAI,6FAAY,MAAhB,IAAI,CAAc,CAAC;IACrB,CAAC;IAES,UAAU;QAClB,IAAI,IAAI,CAAC,KAAK;YAAE,OAAO;QACvB,uBAAA,IAAI,+DAAyB,MAA7B,IAAI,CAA2B,CAAC;QAChC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IACxB,CAAC;IAED,IAAI,KAAK;QACP,OAAO,uBAAA,IAAI,6CAAO,CAAC;IACrB,CAAC;IAED,IAAI,OAAO;QACT,OAAO,uBAAA,IAAI,+CAAS,CAAC;IACvB,CAAC;IAED,IAAI,OAAO;QACT,OAAO,uBAAA,IAAI,+CAAS,CAAC;IACvB,CAAC;IAED,KAAK;QACH,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;IAED;;;;;;OAMG;IACH,EAAE,CACA,KAAY,EACZ,QAAqD;QAErD,MAAM,SAAS,GACb,uBAAA,IAAI,iDAAW,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAA,IAAI,iDAAW,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;QAC1D,SAAS,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;OAMG;IACH,GAAG,CACD,KAAY,EACZ,QAAqD;QAErD,MAAM,SAAS,GAAG,uBAAA,IAAI,iDAAW,CAAC,KAAK,CAAC,CAAC;QACzC,IAAI,CAAC,SAAS;YAAE,OAAO,IAAI,CAAC;QAC5B,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;QAClE,IAAI,KAAK,IAAI,CAAC;YAAE,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC3C,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,IAAI,CACF,KAAY,EACZ,QAAqD;QAErD,MAAM,SAAS,GACb,uBAAA,IAAI,iDAAW,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAA,IAAI,iDAAW,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;QAC1D,SAAS,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;QACzC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;;;OAUG;IACH,OAAO,CACL,KAAY;QAMZ,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,uBAAA,IAAI,0DAA2B,IAAI,MAAA,CAAC;YACpC,IAAI,KAAK,KAAK,OAAO;gBAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAClD,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,OAAc,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,IAAI;QACR,uBAAA,IAAI,0DAA2B,IAAI,MAAA,CAAC;QACpC,MAAM,uBAAA,IAAI,kDAAY,CAAC;IACzB,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,uBAAA,IAAI,8DAAwB,CAAC;IACtC,CAAC;IAWD;;;OAGG;IACH,KAAK,CAAC,YAAY;QAChB,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,OAAO,uBAAA,IAAI,kGAAiB,MAArB,IAAI,CAAmB,CAAC;IACjC,CAAC;IAkBD;;;;OAIG;IACH,KAAK,CAAC,SAAS;QACb,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,OAAO,uBAAA,IAAI,+FAAc,MAAlB,IAAI,CAAgB,CAAC;IAC9B,CAAC;IAuBS,KAAK,CACb,KAAY,EACZ,GAAG,IAA6D;QAEhE,6EAA6E;QAC7E,IAAI,uBAAA,IAAI,6CAAO;YAAE,OAAO;QAExB,IAAI,KAAK,KAAK,KAAK,EAAE;YACnB,uBAAA,IAAI,yCAAU,IAAI,MAAA,CAAC;YACnB,uBAAA,IAAI,yDAAmB,MAAvB,IAAI,CAAqB,CAAC;SAC3B;QAED,MAAM,SAAS,GAAoE,uBAAA,IAAI,iDAAW,CAAC,KAAK,CAAC,CAAC;QAC1G,IAAI,SAAS,EAAE;YACb,uBAAA,IAAI,iDAAW,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAQ,CAAC;YACjE,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAO,EAAE,EAAE,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;SAC7D;QAED,IAAI,KAAK,KAAK,OAAO,EAAE;YACrB,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAsB,CAAC;YAC3C,IAAI,CAAC,uBAAA,IAAI,8DAAwB,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE;gBACvD,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aACvB;YACD,uBAAA,IAAI,8DAAwB,MAA5B,IAAI,EAAyB,KAAK,CAAC,CAAC;YACpC,uBAAA,IAAI,wDAAkB,MAAtB,IAAI,EAAmB,KAAK,CAAC,CAAC;YAC9B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAClB,OAAO;SACR;QAED,IAAI,KAAK,KAAK,OAAO,EAAE;YACrB,yEAAyE;YAEzE,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAmB,CAAC;YACxC,IAAI,CAAC,uBAAA,IAAI,8DAAwB,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE;gBACvD,mFAAmF;gBACnF,8EAA8E;gBAC9E,kCAAkC;gBAClC,wBAAwB;gBACxB,4BAA4B;gBAC5B,SAAS;gBACT,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aACvB;YACD,uBAAA,IAAI,8DAAwB,MAA5B,IAAI,EAAyB,KAAK,CAAC,CAAC;YACpC,uBAAA,IAAI,wDAAkB,MAAtB,IAAI,EAAmB,KAAK,CAAC,CAAC;YAC9B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SACnB;IACH,CAAC;IAES,UAAU;QAClB,MAAM,6BAA6B,GAAG,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACnE,IAAI,6BAA6B,EAAE;YACjC,IAAI,CAAC,KAAK,CAAC,+BAA+B,EAAE,uBAAA,IAAI,kGAAiB,MAArB,IAAI,CAAmB,CAAC,CAAC;SACtE;IACH,CAAC;IAqDS,KAAK,CAAC,mBAAmB,CACjC,cAA8B,EAC9B,OAA6B;QAE7B,MAAM,MAAM,GAAG,OAAO,EAAE,MAAM,CAAC;QAC/B,IAAI,MAAM,EAAE;YACV,IAAI,MAAM,CAAC,OAAO;gBAAE,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YAC5C,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC;SACjE;QACD,uBAAA,IAAI,+FAAc,MAAlB,IAAI,CAAgB,CAAC;QACrB,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,MAAM,MAAM,GAAG,MAAM,CAAC,kBAAkB,CACtC,cAAc,EACd,IAAI,CAAC,UAAU,CAChB,CAAC;QACF,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE;YAChC,uBAAA,IAAI,iGAAgB,MAApB,IAAI,EAAiB,KAAK,CAAC,CAAC;SAC7B;QACD,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE;YACrC,MAAM,IAAI,iBAAiB,EAAE,CAAC;SAC/B;QACD,uBAAA,IAAI,6FAAY,MAAhB,IAAI,CAAc,CAAC;IACrB,CAAC;IA4DD;QA5PE,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;YACtC,MAAM,IAAI,cAAc,CACtB,+EAA+E,CAChF,CAAC;SACH;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE,CAAC;IACvC,CAAC;QAYC,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;YACtC,MAAM,IAAI,cAAc,CACtB,+EAA+E,CAChF,CAAC;SACH;QACD,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB;aACrC,EAAE,CAAC,CAAC,CAAC,CAAE;aACP,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,EAAsB,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC;aACpE,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC9B,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YAC3B,MAAM,IAAI,cAAc,CAAC,+DAA+D,CAAC,CAAC;SAC3F;QACD,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;QAyFC,IAAI,IAAI,CAAC,KAAK;YAAE,OAAO;QACvB,uBAAA,IAAI,0DAA2B,SAAS,MAAA,CAAC;IAC3C,CAAC,2GACe,KAA6C;QAC3D,IAAI,IAAI,CAAC,KAAK;YAAE,OAAO;QACvB,MAAM,eAAe,GAAG,uBAAA,IAAI,oGAAmB,MAAvB,IAAI,EAAoB,KAAK,CAAC,CAAC;QACvD,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;QAElD,QAAQ,KAAK,CAAC,IAAI,EAAE;YAClB,KAAK,qBAAqB,CAAC,CAAC;gBAC1B,MAAM,OAAO,GAAG,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE,CAAC;gBAChD,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,YAAY,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE;oBAChE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;iBAC1D;qBAAM,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,kBAAkB,IAAI,OAAO,CAAC,IAAI,KAAK,UAAU,EAAE;oBACjF,IAAI,OAAO,CAAC,KAAK,EAAE;wBACjB,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,KAAK,CAAC,YAAY,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;qBAClE;iBACF;gBACD,MAAM;aACP;YACD,KAAK,cAAc,CAAC,CAAC;gBACnB,IAAI,CAAC,iCAAiC,CAAC,eAAe,CAAC,CAAC;gBACxD,IAAI,CAAC,4BAA4B,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;gBACzD,MAAM;aACP;YACD,KAAK,oBAAoB,CAAC,CAAC;gBACzB,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;gBAC5D,MAAM;aACP;YACD,KAAK,eAAe,CAAC,CAAC;gBACpB,uBAAA,IAAI,0DAA2B,eAAe,MAAA,CAAC;gBAC/C,MAAM;aACP;YACD,KAAK,qBAAqB,CAAC;YAC3B,KAAK,eAAe;gBAClB,MAAM;SACT;IACH,CAAC;QAEC,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,MAAM,IAAI,cAAc,CAAC,yCAAyC,CAAC,CAAC;SACrE;QACD,MAAM,QAAQ,GAAG,uBAAA,IAAI,8DAAwB,CAAC;QAC9C,IAAI,CAAC,QAAQ,EAAE;YACb,MAAM,IAAI,cAAc,CAAC,0CAA0C,CAAC,CAAC;SACtE;QACD,uBAAA,IAAI,0DAA2B,SAAS,MAAA,CAAC;QACzC,OAAO,QAAQ,CAAC;IAClB,CAAC,iHA+BkB,KAA6C;QAC9D,IAAI,QAAQ,GAAG,uBAAA,IAAI,8DAAwB,CAAC;QAE5C,IAAI,KAAK,CAAC,IAAI,KAAK,eAAe,EAAE;YAClC,IAAI,QAAQ,EAAE;gBACZ,MAAM,IAAI,cAAc,CAAC,+BAA+B,KAAK,CAAC,IAAI,kCAAkC,CAAC,CAAC;aACvG;YACD,OAAO,KAAK,CAAC,OAAO,CAAC;SACtB;QAED,IAAI,CAAC,QAAQ,EAAE;YACb,MAAM,IAAI,cAAc,CAAC,+BAA+B,KAAK,CAAC,IAAI,yBAAyB,CAAC,CAAC;SAC9F;QAED,QAAQ,KAAK,CAAC,IAAI,EAAE;YAClB,KAAK,cAAc;gBACjB,OAAO,QAAQ,CAAC;YAClB,KAAK,eAAe;gBAClB,QAAQ,CAAC,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC;gBAC/C,QAAQ,CAAC,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC;gBACnD,QAAQ,CAAC,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC;gBACzD,OAAO,QAAQ,CAAC;YAClB,KAAK,qBAAqB;gBACxB,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;gBAC3C,OAAO,QAAQ,CAAC;YAClB,KAAK,qBAAqB,CAAC,CAAC;gBAC1B,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACzD,IAAI,eAAe,EAAE,IAAI,KAAK,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE;oBACzE,eAAe,CAAC,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC;iBAC1C;qBAAM,IAAI,eAAe,EAAE,IAAI,KAAK,UAAU,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,kBAAkB,EAAE;oBAC1F,sEAAsE;oBACtE,qEAAqE;oBACrE,0CAA0C;oBAC1C,IAAI,OAAO,GAAI,eAAuB,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;oBAChE,OAAO,IAAI,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC;oBAEpC,MAAM,CAAC,cAAc,CAAC,eAAe,EAAE,iBAAiB,EAAE;wBACxD,KAAK,EAAE,OAAO;wBACd,UAAU,EAAE,KAAK;wBACjB,QAAQ,EAAE,IAAI;qBACf,CAAC,CAAC;oBAEH,IAAI,OAAO,EAAE;wBACX,eAAe,CAAC,KAAK,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;qBAC/C;iBACF;gBACD,OAAO,QAAQ,CAAC;aACjB;YACD,KAAK,oBAAoB;gBACvB,OAAO,QAAQ,CAAC;SACnB;IACH,CAAC,EAEA,MAAM,CAAC,aAAa,EAAC;QACpB,MAAM,SAAS,GAA6C,EAAE,CAAC;QAC/D,MAAM,SAAS,GAGT,EAAE,CAAC;QACT,IAAI,IAAI,GAAG,KAAK,CAAC;QAEjB,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,KAAK,EAAE,EAAE;YAC/B,MAAM,MAAM,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;YACjC,IAAI,MAAM,EAAE;gBACV,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;aACvB;iBAAM;gBACL,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACvB;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YAClB,IAAI,GAAG,IAAI,CAAC;YACZ,KAAK,MAAM,MAAM,IAAI,SAAS,EAAE;gBAC9B,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;aAC3B;YACD,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;YACvB,IAAI,GAAG,IAAI,CAAC;YACZ,KAAK,MAAM,MAAM,IAAI,SAAS,EAAE;gBAC9B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;aACpB;YACD,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;YACvB,IAAI,GAAG,IAAI,CAAC;YACZ,KAAK,MAAM,MAAM,IAAI,SAAS,EAAE;gBAC9B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;aACpB;YACD,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,IAAI,EAAE,KAAK,IAAqE,EAAE;gBAChF,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;oBACrB,IAAI,IAAI,EAAE;wBACR,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;qBACzC;oBACD,OAAO,IAAI,OAAO,CAAqD,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,CACzF,SAAS,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CACpC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;iBAC/F;gBACD,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,EAAG,CAAC;gBACjC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;YACvC,CAAC;YACD,MAAM,EAAE,KAAK,IAAI,EAAE;gBACjB,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;YAC1C,CAAC;SACF,CAAC;IACJ,CAAC;IAED,gBAAgB;QACd,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAClF,OAAO,MAAM,CAAC,gBAAgB,EAAE,CAAC;IACnC,CAAC;CACF"}