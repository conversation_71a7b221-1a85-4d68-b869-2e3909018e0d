import{l as Ye,f as Je,a as Ze,t as _,b as i,A as Se,C as b,m as O,a5 as Be,D as ye,J as h,T as se,S as ae,O as N,V as le,M as p,G as Q,H as R,L as o,K as y,Z as e,I as ke,X as A,W as V,_ as Ce,a2 as x,aa as Ge,P as et,F as tt,R as Ne,u as Ee,a3 as Qe,a1 as Ve,ay as st,v as Ue,aj as at,ak as nt,az as rt}from"./SpinnerAugment-C3d3R_8C.js";import"./design-system-init-s3QUMN0e.js";/* empty css                                */import{h as it,I as Me,e as ue,i as ot,c as lt}from"./IconButtonAugment-BYROpfM6.js";import{M as dt}from"./message-broker-BVKpqRQ5.js";import{S as ct,T as vt,a as Ke,b as gt,c as je,d as ut,v as mt,e as pt}from"./StatusIndicator-Dv6MlzkV.js";import{a as Z,s as wt,R as Oe}from"./index-C4gKbsWy.js";import{T as Te,a as oe,C as ft}from"./CardAugment-L1_52yiK.js";import{C as ht}from"./CalloutAugment-C1fpFxhd.js";import{E as _t}from"./exclamation-triangle-DqFxE6O2.js";import{d as $t,s as St,R as qe}from"./remote-agents-client-Bz3pE78Q.js";import{A as yt}from"./augment-logo-BwZlCwrx.js";import"./async-messaging-BeBg25ZO.js";import"./types-CGlLNakm.js";var kt=Je("<svg><!></svg>"),bt=h(" <!>",1),xt=h('<div class="agent-card-footer svelte-1qwlkoj"><!> <div class="time-container"><!></div></div>'),At=h('<div class="task-text-container svelte-1tatwxk"><!></div>'),Rt=h('<div class="task-status-indicator svelte-1tatwxk"><!></div>'),Pt=h('<div class="task-item svelte-1tatwxk"><div></div> <!> <!></div>'),zt=h(' <button class="error-dismiss svelte-1bxdvw4" aria-label="Dismiss error">×</button>',1),Ht=h('<div class="deletion-error svelte-1bxdvw4"><!></div>'),Ft=h('<span class="setup-script-title svelte-1bxdvw4">Generate a setup script</span>'),It=h('<div class="setup-script-title-container svelte-1bxdvw4"><div class="setup-script-badge svelte-1bxdvw4"><!></div> <!></div>'),Ot=h('<div class="tasks-list svelte-1bxdvw4"></div>'),Tt=h('<div class="card-header svelte-1bxdvw4"><div class="session-summary-container svelte-1bxdvw4"><!></div> <div class="card-info"><!></div></div> <div class="card-content svelte-1bxdvw4"><!></div> <div class="card-actions svelte-1bxdvw4"><!> <!> <!></div> <!>',1),qt=h("<div><!> <!></div>");function Ct(de,S){Se(S,!1);const T=O(),H=O(),P=O(),M=O();let c=b(S,"agent",8),X=b(S,"selected",8,!1),F=b(S,"isPinned",8,!1),I=b(S,"onSelect",8),j=b(S,"onDelete",8),q=b(S,"deletionError",12,null),B=b(S,"isDeleting",8,!1),v=b(S,"onTogglePinned",24,()=>{}),E=b(S,"sshConfig",24,()=>{});function G(){q(null)}Be(()=>{G()}),V(()=>(e(T),e(H),y(E())),()=>{var $;$=E()||{onSSH:()=>Promise.resolve(!1),canSSH:!1},A(T,$.onSSH),A(H,$.canSSH)}),V(()=>y(c()),()=>{A(P,c().turn_summaries||[])}),V(()=>{},()=>{A(M,!0)}),Ce(),ye();var d=qt();let ce;var me=_(d),g=$=>{var ne=Ht(),k=_(ne);ht(k,{variant:"soft",color:"error",size:1,children:(s,l)=>{var U=zt(),ee=Q(U),D=p(ee);N(()=>le(ee,`${q()??""} `)),Ne("click",D,G),i(s,U)},$$slots:{default:!0,icon:(s,l)=>{_t(s,{slot:"icon"})}}}),i($,ne)};R(me,$=>{q()&&$(g)});var Y=p(me,2);ft(Y,{variant:"surface",size:2,interactive:!0,class:"agent-card",$$events:{click:()=>I()(c().remote_agent_id),keydown:$=>$.key==="Enter"&&I()(c().remote_agent_id)},children:($,ne)=>{var k=Tt(),s=Q(k),l=_(s),U=_(l),ee=a=>{var r=It(),f=_(r),n=_(f);Ke(n);var t=p(f,2);se(t,{size:2,weight:"medium",children:(m,u)=>{var K=Ft();i(m,K)},$$slots:{default:!0}}),i(a,r)},D=a=>{se(a,{size:2,weight:"medium",class:"session-text",children:(r,f)=>{var n=ae();N(()=>le(n,(y(c()),o(()=>c().session_summary)))),i(r,n)},$$slots:{default:!0}})};R(U,a=>{y(c()),o(()=>c().is_setup_script_agent)?a(ee):a(D,!1)});var L=p(l,2),ve=_(L);ct(ve,{get status(){return y(c()),o(()=>c().status)},get workspaceStatus(){return y(c()),o(()=>c().workspace_status)},isExpanded:!0,get hasUpdates(){return y(c()),o(()=>c().has_updates)}});var re=p(s,2),be=_(re),xe=a=>{var r=Ot();ue(r,5,()=>(e(P),o(()=>e(P).slice(0,3))),ot,(f,n)=>{(function(t,m){Se(m,!1);const u=O();let K=b(m,"text",8),W=b(m,"status",8,"info");V(()=>y(W()),()=>{A(u,function(C){switch(C){case"success":return"task-success";case"warning":return"task-warning";case"error":return"task-error";default:return"task-info"}}(W()))}),Ce(),ye();var pe=Pt(),we=_(pe),z=p(we,2);const Re=x(()=>(y(oe),o(()=>[oe.Hover])));Te(z,{get content(){return K()},get triggerOn(){return e(Re)},maxWidth:"400px",children:(C,ge)=>{var he=At(),J=_(he);se(J,{size:1,color:"secondary",children:(ie,Xe)=>{var Pe=ae();N(()=>le(Pe,K())),i(ie,Pe)},$$slots:{default:!0}}),i(C,he)},$$slots:{default:!0}});var te=p(z,2),fe=C=>{var ge=Rt(),he=_(ge);const J=x(()=>W()==="error"?"error":"neutral");se(he,{size:1,get color(){return e(J)},children:(ie,Xe)=>{var Pe=ae();N(()=>le(Pe,W()==="error"?"!":W()==="warning"?"⚠":"")),i(ie,Pe)},$$slots:{default:!0}}),i(C,ge)};R(te,C=>{W()!=="error"&&W()!=="warning"||C(fe)}),N(()=>Ge(we,1,`bullet-point ${e(u)??""}`,"svelte-1tatwxk")),i(t,pe),ke()})(f,{get text(){return e(n)},status:"success"})}),i(a,r)};R(be,a=>{e(P),o(()=>e(P).length>0)&&a(xe)});var Ae=p(re,2),ze=_(Ae),He=a=>{const r=x(()=>F()?"Unpin agent":"Pin agent"),f=x(()=>(y(oe),o(()=>[oe.Hover])));Te(a,{get content(){return e(r)},get triggerOn(){return e(f)},side:"top",children:(n,t)=>{Me(n,{variant:"ghost",color:"neutral",size:1,$$events:{click:m=>{m.stopPropagation(),v()()}},children:(m,u)=>{var K=tt(),W=Q(K),pe=z=>{(function(Re,te){const fe=Ye(te,["children","$$slots","$$events","$$legacy"]);var C=kt();Ze(C,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 640 512",...fe}));var ge=_(C);it(ge,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2s-6.3 25.5 4.1 33.7l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L481.4 352c9.4-.4 18.1-4.9 23.9-12.3 6.1-7.8 8.2-17.9 5.8-27.5l-6.2-25c-10.3-41.3-35.4-75.7-68.7-98.3L428.9 96l-3.7-48H456c4.4 0 8.6-1.2 12.2-3.3 7-4.2 11.8-11.9 11.8-20.7 0-13.3-10.7-24-24-24H184c-13.3 0-24 10.7-24 24 0 8.8 4.8 16.5 11.8 20.7 3.6 2.1 7.7 3.3 12.2 3.3h30.8l-3.7 48-3.2 41.6zm214.5 168.1 9.3-121.5c.1-1.2.1-2.5.1-3.7h114.5c0 1.2 0 2.5.1 3.7l10.8 140.9c1.1 14.6 8.8 27.8 20.9 36 23.9 16.2 41.7 40.8 49.1 70.2l1.3 5.1H420l-76-59.6V216c0-13.3-10.7-24-24-24-10.4 0-19.2 6.6-22.6 15.8l-44.2-34.6zM344 367l-80-63h-83.5l1.3-5.1c4-16.1 11.2-30.7 20.7-43.3l-37.7-29.7c-13.7 17.8-23.9 38.6-29.6 61.4l-6.2 25c-2.4 9.6-.2 19.7 5.8 27.5s15.4 12.3 25.2 12.3h136v136c0 13.3 10.7 24 24 24s24-10.7 24-24v-121z"/>',!0),i(Re,C)})(z,{})},we=z=>{gt(z,{})};R(W,z=>{F()?z(pe):z(we,!1)}),i(m,K)},$$slots:{default:!0}})},$$slots:{default:!0}})};R(ze,a=>{v()&&a(He)});var Fe=p(ze,2),Ie=a=>{const r=x(()=>(y(oe),o(()=>[oe.Hover])));Te(a,{content:"SSH to agent",get triggerOn(){return e(r)},side:"top",children:(f,n)=>{const t=x(()=>!e(H)),m=x(()=>e(H)?"SSH to agent":"SSH to agent (agent must be running or idle)");Me(f,{get disabled(){return e(t)},variant:"ghost",color:"neutral",size:1,get title(){return e(m)},$$events:{click:u=>{u.stopPropagation(),e(T)()}},children:(u,K)=>{Ke(u)},$$slots:{default:!0}})},$$slots:{default:!0}})};R(Fe,a=>{E()&&a(Ie)});var De=p(Fe,2);const Le=x(()=>(y(oe),o(()=>[oe.Hover])));Te(De,{content:"Delete agent",get triggerOn(){return e(Le)},side:"top",children:(a,r)=>{const f=x(()=>B()?"Deleting agent...":"Delete agent");Me(a,{variant:"ghost",color:"neutral",size:1,get disabled(){return B()},get title(){return e(f)},$$events:{click:n=>{n.stopPropagation(),j()()}},children:(n,t)=>{vt(n)},$$slots:{default:!0}})},$$slots:{default:!0}});var We=p(Ae,2);const w=x(()=>(y(c()),o(()=>c().updated_at||c().started_at)));(function(a,r){Se(r,!1);let f=b(r,"isRemote",8,!1),n=b(r,"status",8),t=b(r,"timestamp",8),m=O($t(t()));const u=St(t(),z=>{A(m,z)});Be(()=>{u()}),ye();var K=xt(),W=_(K);se(W,{size:1,color:"secondary",class:"location-text",children:(z,Re)=>{var te=ae();N(()=>le(te,f()?"Running in the cloud":"Running locally")),i(z,te)},$$slots:{default:!0}});var pe=p(W,2),we=_(pe);se(we,{size:1,color:"secondary",class:"time-text",children:(z,Re)=>{var te=bt(),fe=Q(te),C=p(fe),ge=J=>{var ie=ae();N(()=>le(ie,e(m))),i(J,ie)},he=J=>{var ie=ae("Unknown time");i(J,ie)};R(C,J=>{t()?J(ge):J(he,!1)}),N(()=>le(fe,`${y(n()),y(Z),o(()=>n()===Z.agentRunning?"Last updated":"Started")??""} `)),i(z,te)},$$slots:{default:!0}}),i(a,K),ke()})(We,{get isRemote(){return e(M)},get status(){return y(c()),o(()=>c().status)},get timestamp(){return e(w)}}),N(()=>et(l,"title",(y(c()),o(()=>c().is_setup_script_agent?"Generate a setup script":c().session_summary)))),i($,k)},$$slots:{default:!0}}),N($=>ce=Ge(d,1,"card-wrapper svelte-1bxdvw4",null,ce,$),[()=>({"selected-card":X(),"setup-script-card":c().is_setup_script_agent,deleting:B()})],x),i(de,d),ke()}function _e(de,S){Se(S,!1);const[T,H]=Ve(),P=()=>Qe(B,"$sharedWebviewStore",T),M=O(),c=O(),X=O();let F=b(S,"agent",8),I=b(S,"selected",8,!1),j=b(S,"onSelect",8);const q=Ee(qe.key),B=Ee(je);let v=O(!1),E=O(null),G=null;function d(){A(E,null),G&&(clearTimeout(G),G=null)}async function ce(){return!!e(X)&&await(async g=>await q.sshToRemoteAgent(g.remote_agent_id))(F())}V(()=>P(),()=>{var g;A(M,((g=P().state)==null?void 0:g.pinnedAgents)||{})}),V(()=>(e(M),y(F())),()=>{var g;A(c,((g=e(M))==null?void 0:g[F().remote_agent_id])===!0)}),V(()=>(y(F()),Z),()=>{A(X,F().status===Z.agentRunning||F().status===Z.agentIdle)}),Ce(),ye();const me=x(()=>({onSSH:ce,canSSH:e(X)}));Ct(de,{get agent(){return F()},get selected(){return I()},get isPinned(){return e(c)},get onSelect(){return j()},onDelete:()=>async function(g){var $,ne;d(),A(v,!0);const Y=(($=P().state)==null?void 0:$.agentOverviews)||[];try{if(!await q.deleteRemoteAgent(g))throw new Error("Failed to delete agent");if(B.update(k=>{if(k)return{...k,agentOverviews:k.agentOverviews.filter(s=>s.remote_agent_id!==g)}}),(((ne=P().state)==null?void 0:ne.pinnedAgents)||{})[g])try{await q.deletePinnedAgentFromStore(g);const k=await q.getPinnedAgentsFromStore();B.update(s=>{if(s)return{...s,pinnedAgents:k}})}catch(k){console.error("Failed to remove pinned status:",k)}}catch(k){console.error("Failed to delete agent:",k),B.update(s=>{if(s)return{...s,agentOverviews:Y}}),A(E,k instanceof Error?k.message:"Failed to delete agent"),G=setTimeout(()=>{d()},5e3)}finally{A(v,!1)}}(F().remote_agent_id),onTogglePinned:()=>async function(g){try{e(c)?await q.deletePinnedAgentFromStore(g):await q.savePinnedAgentToStore(g,!0);const Y=await q.getPinnedAgentsFromStore();B.update($=>{if($)return{...$,pinnedAgents:Y}})}catch(Y){console.error("Failed to toggle pinned status:",Y)}}(F().remote_agent_id),get sshConfig(){return e(me)},get deletionError(){return e(E)},set deletionError(g){A(E,g)},get isDeleting(){return e(v)},set isDeleting(g){A(v,g)},$$legacy:!0}),ke(),H()}var Et=h('<div class="section-header svelte-1tegnqi"><!></div>');function $e(de,S){let T=b(S,"title",8);var H=Et(),P=_(H);se(P,{size:2,color:"secondary",children:(M,c)=>{var X=ae();N(()=>le(X,T())),i(M,X)},$$slots:{default:!0}}),i(de,H)}var Dt=h('<div class="empty-state svelte-aiqmvp"><div class="l-loading-container svelte-aiqmvp"><!> <!></div></div>'),Lt=h('<div class="empty-state svelte-aiqmvp"><!></div>'),Wt=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),Mt=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),jt=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),Bt=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),Gt=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),Ut=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),Kt=h("<!> <!> <!> <!> <!> <!>",1),Nt=h('<div class="agent-list svelte-aiqmvp"><!></div>'),Qt=h('<div class="l-main svelte-1941nw6"><h1 class="l-main__title svelte-1941nw6"><span class="l-main__title-logo svelte-1941nw6"><!></span> Remote Agents</h1> <!></div>');rt(function(de,S){Se(S,!1);const T=new dt(lt),H=new ut(T,void 0,mt,pt);T.registerConsumer(H),Ue(je,H);const P=new qe(T);Ue(qe.key,P),at(()=>(H.fetchStateFromExtension().then(()=>{H.update(I=>{if(!I)return;const j=[...I.activeWebviews,"home"];return I.pinnedAgents?{...I,activeWebviews:j}:{...I,activeWebviews:j,pinnedAgents:{}}})}),()=>{T.dispose(),P.dispose()})),ye();var M=Qt();Ne("message",nt,function(...I){var j;(j=T.onMessageFromExtension)==null||j.apply(this,I)});var c=_(M),X=_(c),F=_(X);yt(F),function(I,j){Se(j,!1);const[q,B]=Ve(),v=()=>Qe(ce,"$sharedWebviewStore",q),E=O(),G=O(),d=O(),ce=Ee(je),me=Ee(qe.key);function g(s){ce.update(l=>{if(l)return{...l,selectedAgentId:s}})}V(()=>v(),()=>{var s;A(E,wt(((s=v().state)==null?void 0:s.agentOverviews)||[]))}),V(()=>v(),()=>{var s;A(G,((s=v().state)==null?void 0:s.pinnedAgents)||{})}),V(()=>(e(E),e(G),Oe),()=>{A(d,e(E).reduce((s,l)=>{var U;return((U=e(G))==null?void 0:U[l.remote_agent_id])===!0?s.pinned.push(l):l.status===Z.agentIdle&&l.has_updates?s.readyToReview.push(l):l.status===Z.agentRunning||l.status===Z.agentStarting||l.workspace_status===Oe.workspaceResuming?s.running.push(l):l.status===Z.agentFailed?s.failed.push(l):l.status===Z.agentIdle||l.workspace_status===Oe.workspacePaused||l.workspace_status===Oe.workspacePausing?s.idle.push(l):s.additional.push(l),s},{pinned:[],readyToReview:[],running:[],idle:[],failed:[],additional:[]}))}),V(()=>v(),()=>{var s;(s=v().state)!=null&&s.agentOverviews||me.focusAugmentPanel()}),Ce(),ye();var Y=Nt(),$=_(Y),ne=s=>{var l=Dt(),U=_(l),ee=_(U);st(ee,{});var D=p(ee,2);se(D,{size:3,color:"secondary",children:(L,ve)=>{var re=ae("Loading the Augment panel...");i(L,re)},$$slots:{default:!0}}),i(s,l)},k=(s,l)=>{var U=D=>{var L=Lt(),ve=_(L);se(ve,{size:3,color:"secondary",children:(re,be)=>{var xe=ae("No agents available");i(re,xe)},$$slots:{default:!0}}),i(D,L)},ee=D=>{var L=Kt(),ve=Q(L),re=w=>{var a=Wt(),r=Q(a);$e(r,{title:"Pinned"});var f=p(r,2);ue(f,7,()=>(e(d),o(()=>e(d).pinned)),(n,t)=>n.remote_agent_id+t,(n,t)=>{const m=x(()=>(e(t),v(),o(()=>{var u;return e(t).remote_agent_id===((u=v().state)==null?void 0:u.selectedAgentId)})));_e(n,{get agent(){return e(t)},get selected(){return e(m)},onSelect:g})}),i(w,a)};R(ve,w=>{e(d),o(()=>e(d).pinned.length>0)&&w(re)});var be=p(ve,2),xe=w=>{var a=Mt(),r=Q(a);$e(r,{title:"Ready to review"});var f=p(r,2);ue(f,7,()=>(e(d),o(()=>e(d).readyToReview)),(n,t)=>n.remote_agent_id+t,(n,t)=>{const m=x(()=>(e(t),v(),o(()=>{var u;return e(t).remote_agent_id===((u=v().state)==null?void 0:u.selectedAgentId)})));_e(n,{get agent(){return e(t)},get selected(){return e(m)},onSelect:g})}),i(w,a)};R(be,w=>{e(d),o(()=>e(d).readyToReview.length>0)&&w(xe)});var Ae=p(be,2),ze=w=>{var a=jt(),r=Q(a);$e(r,{title:"Running agents"});var f=p(r,2);ue(f,7,()=>(e(d),o(()=>e(d).running)),(n,t)=>n.remote_agent_id+t,(n,t)=>{const m=x(()=>(e(t),v(),o(()=>{var u;return e(t).remote_agent_id===((u=v().state)==null?void 0:u.selectedAgentId)})));_e(n,{get agent(){return e(t)},get selected(){return e(m)},onSelect:g})}),i(w,a)};R(Ae,w=>{e(d),o(()=>e(d).running.length>0)&&w(ze)});var He=p(Ae,2),Fe=w=>{var a=Bt(),r=Q(a);$e(r,{title:"Idle agents"});var f=p(r,2);ue(f,7,()=>(e(d),o(()=>e(d).idle)),(n,t)=>n.remote_agent_id+t,(n,t)=>{const m=x(()=>(e(t),v(),o(()=>{var u;return e(t).remote_agent_id===((u=v().state)==null?void 0:u.selectedAgentId)})));_e(n,{get agent(){return e(t)},get selected(){return e(m)},onSelect:g})}),i(w,a)};R(He,w=>{e(d),o(()=>e(d).idle.length>0)&&w(Fe)});var Ie=p(He,2),De=w=>{var a=Gt(),r=Q(a);$e(r,{title:"Failed agents"});var f=p(r,2);ue(f,7,()=>(e(d),o(()=>e(d).failed)),(n,t)=>n.remote_agent_id+t,(n,t)=>{const m=x(()=>(e(t),v(),o(()=>{var u;return e(t).remote_agent_id===((u=v().state)==null?void 0:u.selectedAgentId)})));_e(n,{get agent(){return e(t)},get selected(){return e(m)},onSelect:g})}),i(w,a)};R(Ie,w=>{e(d),o(()=>e(d).failed.length>0)&&w(De)});var Le=p(Ie,2),We=w=>{var a=Ut(),r=Q(a);$e(r,{title:"Other agents"});var f=p(r,2);ue(f,7,()=>(e(d),o(()=>e(d).additional)),(n,t)=>n.remote_agent_id+t,(n,t)=>{const m=x(()=>(e(t),v(),o(()=>{var u;return e(t).remote_agent_id===((u=v().state)==null?void 0:u.selectedAgentId)})));_e(n,{get agent(){return e(t)},get selected(){return e(m)},onSelect:g})}),i(w,a)};R(Le,w=>{e(d),o(()=>e(d).additional.length>0)&&w(We)}),i(D,L)};R(s,D=>{v(),o(()=>{var L;return((L=v().state)==null?void 0:L.agentOverviews.length)===0})?D(U):D(ee,!1)},l)};R($,s=>{v(),o(()=>{var l;return!((l=v().state)!=null&&l.agentOverviews)})?s(ne):s(k,!1)}),i(I,Y),ke(),B()}(p(c,2),{}),i(de,M),ke()},{target:document.getElementById("app")});
