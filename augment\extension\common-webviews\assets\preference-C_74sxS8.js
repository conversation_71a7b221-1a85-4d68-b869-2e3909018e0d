import{C as b,J as h,H as G,t as o,M as a,O as F,a2 as gt,aa as O,R as A,b as p,V as et,P as ft,A as dt,B as yt,m,aj as wt,aE as pt,Z as t,X as c,W as Y,K as tt,_ as kt,D as mt,L as At,I as ht,G as bt,ak as $t,F as qt,az as Bt}from"./SpinnerAugment-C3d3R_8C.js";import{c as B,W as j}from"./IconButtonAugment-BYROpfM6.js";import{aJ as lt}from"./AugmentMessage-B21_KOYl.js";import{b as Ct,a as xt}from"./BaseTextInput-hjj6nBZd.js";import{C as Mt,S as Rt}from"./folder-opened-AFaV2qWu.js";import{M as It}from"./message-broker-BVKpqRQ5.js";import{s as Wt}from"./chat-context-DL_54yja.js";import{M as Dt}from"./index-BPBpJFCC.js";import"./CalloutAugment-C1fpFxhd.js";import"./CardAugment-L1_52yiK.js";import"./index-BWYp8-tu.js";import"./async-messaging-BeBg25ZO.js";import"./types-CGlLNakm.js";import"./file-paths-BEJIrsZp.js";import"./isObjectLike-DEzymPim.js";import"./index-BiRO5qTg.js";import"./diff-operations-D22Y9QvN.js";import"./svelte-component-ClwSdZAs.js";import"./Filespan-BdXbs49f.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./keypress-DD1aQVr0.js";import"./await-XsDZ1KjX.js";import"./OpenFileButton-CMz0rieg.js";import"./index-C4gKbsWy.js";import"./remote-agents-client-Bz3pE78Q.js";import"./ra-diff-ops-model-D15ceLKW.js";import"./TextAreaAugment-BGi_2d0z.js";import"./ButtonAugment-Cfj8nOxE.js";import"./CollapseButtonAugment-ClJpCixO.js";import"./user-BS1qxHV9.js";import"./MaterialIcon-Ce6mYTQ4.js";import"./CopyButton-DDb8u9Qe.js";import"./ellipsis-v5eG5a7L.js";import"./IconFilePath-J6j-AVyQ.js";import"./LanguageIcon-Byw9lw0X.js";import"./next-edit-types-904A5ehg.js";import"./chevron-down-CsTV-WoH.js";import"./index-B2GSXkDj.js";import"./augment-logo-BwZlCwrx.js";import"./pen-to-square-DGRIEioi.js";import"./check-BsVXgaKr.js";var Ot=h('<div class="header svelte-1894wv4"> </div>'),St=h('<div class="container svelte-1894wv4"><!> <div class="buttons svelte-1894wv4"><button type="button">A</button> <button type="button">A</button> <button type="button">A</button> <button type="button">=</button> <button type="button">B</button> <button type="button">B</button> <button type="button">B</button></div></div>');function nt(H,C){let i=b(C,"selected",12,null),x=b(C,"question",8,null);function v(w){i(w)}var e=St(),d=o(e),S=w=>{var L=Ot(),K=o(L);F(()=>et(K,x())),p(w,L)};G(d,w=>{x()&&w(S)});var M=a(d,2),g=o(M);let f;var r=a(g,2);let $;var y=a(r,2);let s;var R=a(y,2);let V;var _=a(R,2);let X;var z=a(_,2);let J;var N=a(z,2);let Z;F((w,L,K,at,st,l,u)=>{f=O(g,1,"button large svelte-1894wv4",null,f,w),$=O(r,1,"button medium svelte-1894wv4",null,$,L),s=O(y,1,"button small svelte-1894wv4",null,s,K),V=O(R,1,"button equal svelte-1894wv4",null,V,at),X=O(_,1,"button small svelte-1894wv4",null,X,st),J=O(z,1,"button medium svelte-1894wv4",null,J,l),Z=O(N,1,"button large svelte-1894wv4",null,Z,u)},[()=>({highlighted:i()==="A3"}),()=>({highlighted:i()==="A2"}),()=>({highlighted:i()==="A1"}),()=>({highlighted:i()==="="}),()=>({highlighted:i()==="B1"}),()=>({highlighted:i()==="B2"}),()=>({highlighted:i()==="B3"})],gt),A("click",g,()=>v("A3")),A("click",r,()=>v("A2")),A("click",y,()=>v("A1")),A("click",R,()=>v("=")),A("click",_,()=>v("B1")),A("click",z,()=>v("B2")),A("click",N,()=>v("B3")),p(H,e)}var _t=h('<div class="question svelte-1i0f73l"> </div>'),zt=h('<div class="container svelte-1i0f73l"><!> <textarea class="input svelte-1i0f73l" rows="3"></textarea></div>'),Lt=h('<button class="button svelte-2k5n"> </button>'),Pt=h("<div> </div>"),Et=h('<div class="container svelte-n0uy88"><!> <label class="custom-checkbox svelte-n0uy88"><input type="checkbox" class="svelte-n0uy88"/> <span class="svelte-n0uy88"></span></label></div>'),Ft=h("<!> <!> <!> <!> <!> <!>",1),jt=h("<p>Streaming in progress... Please wait for both responses to complete.</p>"),Gt=h('<main><div class="l-pref svelte-751nif"><h1 class="svelte-751nif">Input message</h1> <!> <hr class="l-side-by-side svelte-751nif"/> <div class="l-side-by-side svelte-751nif"><div class="l-side-by-side__child svelte-751nif"><h1 class="svelte-751nif">Option A</h1> <!></div> <div class="divider svelte-751nif"></div> <div class="l-side-by-side__child svelte-751nif"><h1 class="svelte-751nif">Option B</h1> <!></div></div> <hr class="svelte-751nif"/> <!></div></main>');function Ht(H,C){dt(C,!1);const i=m(),x=m(),v=m();let e=b(C,"inputData",8);const d=yt();let S=new Mt(new It(B),B,new Rt);Wt(S);let M=m(null),g=m(null),f=null,r=m(null),$=m(""),y=m(!1),s=m({a:null,b:null}),R=m(e().data.a.response.length>0&&e().data.b.response.length>0);function V(){if(f="=",t(r)===null)return void d("notify","Overall rating is required");const l={overallRating:t(r),formattingRating:t(M)||"=",hallucinationRating:f||"=",instructionFollowingRating:t(g)||"=",isHighQuality:t(y),textFeedback:t($)};d("result",l)}wt(()=>{window.addEventListener("message",l=>{const u=l.data;u.type===j.chatModelReply?(u.stream==="A"?pt(s,t(s).a=u.data.text):u.stream==="B"&&pt(s,t(s).b=u.data.text),c(s,t(s))):u.type===j.chatStreamDone&&c(R,!0)})}),Y(()=>t(r),()=>{var l;c(i,(l=t(r))==="="||l===null?"Is this a high quality comparison?":`Are you completely happy with response '${l.startsWith("A")?"A":"B"}'?`)}),Y(()=>(t(s),tt(e())),()=>{c(x,t(s).a!==null?t(s).a:e().data.a.response)}),Y(()=>(t(s),tt(e())),()=>{c(v,t(s).b!==null?t(s).b:e().data.b.response)}),Y(()=>tt(e()),()=>{c(R,e().data.a.response.length>0&&e().data.b.response.length>0)}),kt(),mt();var _=Gt(),X=o(_),z=a(o(X),2);lt(z,{get markdown(){return tt(e()),At(()=>e().data.a.message)}});var J=a(z,4),N=o(J),Z=a(o(N),2);lt(Z,{get markdown(){return t(x)}});var w=a(N,4),L=a(o(w),2);lt(L,{get markdown(){return t(v)}});var K=a(J,4),at=l=>{var u=Ft(),ot=bt(u);nt(ot,{question:"Which response is formatted better? (e.g. level of detail style, structure)?",get selected(){return t(M)},set selected(n){c(M,n)},$$legacy:!0});var rt=a(ot,2);nt(rt,{question:"Which response follows your instruction better?",get selected(){return t(g)},set selected(n){c(g,n)},$$legacy:!0});var ut=a(rt,2);nt(ut,{question:"Which response is better overall?",get selected(){return t(r)},set selected(n){c(r,n)},$$legacy:!0});var vt=a(ut,2);(function(n,k){let Q=b(k,"isChecked",12,!1),I=b(k,"question",8,null);var q=Et(),W=o(q),P=D=>{var U=Pt(),it=o(U);F(()=>et(it,I())),p(D,U)};G(W,D=>{I()&&D(P)});var E=a(W,2),T=o(E);xt(T,Q),p(n,q)})(vt,{get question(){return t(i)},get isChecked(){return t(y)},set isChecked(n){c(y,n)},$$legacy:!0});var ct=a(vt,2);(function(n,k){let Q=b(k,"value",12,""),I=b(k,"question",8,null),q=b(k,"placeholder",8,"");var W=zt(),P=o(W),E=D=>{var U=_t(),it=o(U);F(()=>et(it,I())),p(D,U)};G(P,D=>{I()&&D(E)});var T=a(P,2);F(()=>ft(T,"placeholder",q())),Ct(T,Q),p(n,W)})(ct,{question:"Any additional feedback?",placeholder:"Please explain your answers to the above questions.",get value(){return t($)},set value(n){c($,n)},$$legacy:!0}),function(n,k){let Q=b(k,"label",8,"Submit"),I=b(k,"onClick",8);var q=Lt(),W=o(q);F(()=>et(W,Q())),A("click",q,function(...P){var E;(E=I())==null||E.apply(this,P)}),p(n,q)}(a(ct,2),{label:"Submit",onClick:V}),p(l,u)},st=l=>{var u=jt();p(l,u)};G(K,l=>{t(R)?l(at):l(st,!1)}),p(H,_),ht()}var Jt=h("<main><!></main>");function Nt(H,C){dt(C,!1);let i=m();function x(e){const d=e.detail;B.postMessage({type:j.preferenceResultMessage,data:d})}function v(e){B.postMessage({type:j.preferenceNotify,data:e.detail})}B.postMessage({type:j.preferencePanelLoaded}),mt(),A("message",$t,function(e){const d=e.data;d.type===j.preferenceInit&&c(i,d.data)}),Dt.Root(H,{children:(e,d)=>{var S=Jt(),M=o(S),g=f=>{var r=qt(),$=bt(r),y=s=>{Ht(s,{get inputData(){return t(i)},$$events:{result:x,notify:v}})};G($,s=>{t(i).type==="Chat"&&s(y)}),p(f,r)};G(M,f=>{t(i)&&f(g)}),p(e,S)},$$slots:{default:!0}}),ht()}(async function(){B&&B.initialize&&await B.initialize(),Bt(Nt,{target:document.getElementById("app")})})();
