<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment - Tool Configuration</title>
    <script nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <script type="module" crossorigin src="./assets/settings-D4XAQxQe.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-C3d3R_8C.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-s3QUMN0e.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-BYROpfM6.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-BeBg25ZO.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-BVKpqRQ5.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-BEJIrsZp.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/isObjectLike-DEzymPim.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-L1_52yiK.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/BaseTextInput-hjj6nBZd.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/index-BWYp8-tu.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/user-BS1qxHV9.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/download-DEUSNPpd.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/VSCodeCodicon-Cl6-aUcf.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/svelte-component-ClwSdZAs.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/CollapseButtonAugment-ClJpCixO.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/index-B2GSXkDj.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/ellipsis-v5eG5a7L.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/Drawer-BoX5ZBmi.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-Cfj8nOxE.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/CalloutAugment-C1fpFxhd.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/pen-to-square-DGRIEioi.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-BGi_2d0z.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/chevron-down-CsTV-WoH.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/index-BiRO5qTg.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/index-BPBpJFCC.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/MarkdownEditor-B2OO4mz6.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/RulesModeSelector-DrwB5ULH.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/ModalAugment-CcvowbRG.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DoxdFmoV.css" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-DgOX1UWm.css" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-B4afvB2A.css" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="stylesheet" crossorigin href="./assets/user-dXUx3CYB.css" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="stylesheet" crossorigin href="./assets/index-D9au8v71.css" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="stylesheet" crossorigin href="./assets/download-C1VayJBB.css" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="stylesheet" crossorigin href="./assets/VSCodeCodicon-DVaocTud.css" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="stylesheet" crossorigin href="./assets/CollapseButtonAugment-BX1Qki-o.css" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="stylesheet" crossorigin href="./assets/Drawer-u8LRIFRf.css" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-DRIZURB3.css" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-BcSV_kHI.css" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="stylesheet" crossorigin href="./assets/CalloutAugment-C-z-uXWx.css" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-k8sG2hbx.css" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="stylesheet" crossorigin href="./assets/index-BlHvDt2c.css" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="stylesheet" crossorigin href="./assets/BaseTextInput-CEzLOEg8.css" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="stylesheet" crossorigin href="./assets/MarkdownEditor-B6vv3aGc.css" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="stylesheet" crossorigin href="./assets/RulesModeSelector-Qv_62MPy.css" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="stylesheet" crossorigin href="./assets/ModalAugment-CM3byOYD.css" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="stylesheet" crossorigin href="./assets/settings-CGCCAaSg.css" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
