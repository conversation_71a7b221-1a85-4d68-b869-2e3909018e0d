<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment - Next Edit Suggestions</title>
    <script nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <script type="module" crossorigin src="./assets/next-edit-suggestions-D6nFREIN.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-C3d3R_8C.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-s3QUMN0e.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/await-XsDZ1KjX.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/index-B2GSXkDj.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-BwZlCwrx.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/index-BiRO5qTg.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="modulepreload" crossorigin href="./assets/index-BPBpJFCC.js" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DoxdFmoV.css" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-DgOX1UWm.css" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="stylesheet" crossorigin href="./assets/index-BlHvDt2c.css" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
    <link rel="stylesheet" crossorigin href="./assets/next-edit-suggestions-Df4-uiQ1.css" nonce="nonce-zPguDUWmMx5RMFonMIFSCg==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
