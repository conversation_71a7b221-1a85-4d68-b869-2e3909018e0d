import{l as Ne,f as Be,a as De,t as b,b as n,A as fe,C as ie,aj as ze,w as ee,W as me,X as g,a3 as P,m as h,Z as e,K as pe,_ as He,D as he,F as Re,G as ne,H as te,I as $e,a1 as Ee,J,al as Me,S as V,O as Le,V as be,L as W,a2 as A,M as se,a4 as _e,T as Ie,a5 as je,R as We,ak as qe,az as Pe}from"./SpinnerAugment-C3d3R_8C.js";import"./design-system-init-s3QUMN0e.js";import{h as Ue,c as U,W as ye,e as Ve,i as Je}from"./IconButtonAugment-BYROpfM6.js";import{O as Ke}from"./OpenFileButton-CMz0rieg.js";import{S as Xe}from"./TextAreaAugment-BGi_2d0z.js";import{C as Ze}from"./check-BsVXgaKr.js";import{C as Te,E as ke,D as q,R as Ye,M as ge,f as Qe,g as et,h as tt}from"./index-BWYp8-tu.js";import{M as we}from"./message-broker-BVKpqRQ5.js";import{M as st}from"./MarkdownEditor-B2OO4mz6.js";import{B as Fe}from"./ButtonAugment-Cfj8nOxE.js";import{C as Ge}from"./chevron-down-CsTV-WoH.js";import{F as ot}from"./Filespan-BdXbs49f.js";import{T as Se,a as re}from"./CardAugment-L1_52yiK.js";import"./chat-context-DL_54yja.js";import"./index-C4gKbsWy.js";import"./index-BiRO5qTg.js";import"./remote-agents-client-Bz3pE78Q.js";import"./types-CGlLNakm.js";import"./ra-diff-ops-model-D15ceLKW.js";import"./BaseTextInput-hjj6nBZd.js";import"./async-messaging-BeBg25ZO.js";import"./file-paths-BEJIrsZp.js";import"./isObjectLike-DEzymPim.js";var at=Be("<svg><!></svg>");function xe(K,z){const $=Ne(z,["children","$$slots","$$events","$$legacy"]);var E=at();De(E,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...$}));var i=b(E);Ue(i,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M440.6 273.4c4.7-4.5 7.4-10.8 7.4-17.4s-2.7-12.8-7.4-17.4l-176-168c-9.6-9.2-24.8-8.8-33.9.8s-8.8 24.8.8 33.9L364.1 232H24c-13.3 0-24 10.7-24 24s10.7 24 24 24h340.1L231.4 406.6c-9.6 9.2-9.9 24.3-.8 33.9s24.3 9.9 33.9.8l176-168z"/>',!0),n(K,E)}var nt=J("<!> <!>",1),rt=J('<div class="rules-dropdown-content svelte-18wohv"><!> <!></div>'),it=J("<!> <!>",1);function lt(K,z){fe(z,!1);const[$,E]=Ee(),i=()=>P(s,"$rulesFiles",$),N=()=>P(L,"$selectedRule",$),y=()=>P(e(F),"$focusedIndex",$),M=h(),X=h(),Z=h();let oe=ie(z,"onRuleSelected",8),ae=ie(z,"disabled",8,!1);const le=new we(U),ce=new Te,l=new ke(U,le,ce),s=ee([]),w=ee(!0),L=ee(void 0);let F=h(void 0),B=h(()=>{});ze(()=>{(async function(){try{w.set(!0);const u=await l.findRules("",100);s.set(u)}catch(u){console.error("Failed to load rules:",u),s.set([])}finally{w.set(!1)}})();const d=u=>{var I;((I=u.data)==null?void 0:I.type)===ye.getRulesListResponse&&(s.set(u.data.data||[]),w.set(!1))};return window.addEventListener("message",d),()=>{window.removeEventListener("message",d)}});let D=h(),x=h(!1);function de(d){g(x,d)}me(()=>i(),()=>{g(M,i().length>0)}),me(()=>(pe(ae()),e(M)),()=>{g(X,ae()||!e(M))}),me(()=>e(M),()=>{g(Z,e(M)?"Move highlighted text to a .augment/rules file":"Please add at least 1 file to .augment/rules and reload VSCode")}),He(),he();var c=Re(),Y=ne(c),Q=d=>{var u=Re(),I=ne(u),ue=v=>{q.Root(v,{onOpenChange:de,get requestClose(){return e(B)},set requestClose(a){g(B,a)},get focusedIndex(){return e(F)},set focusedIndex(a){_e(g(F,a),"$focusedIndex",$)},children:(a,m)=>{var f=it(),p=ne(f);q.Trigger(p,{children:(T,ve)=>{const _=A(()=>(pe(re),W(()=>[re.Hover]))),j=A(()=>!e(x)&&void 0);Me(Se(T,{get content(){return e(Z)},get triggerOn(){return e(_)},side:"top",get open(){return e(j)},children:(k,C)=>{Fe(k,{color:"neutral",variant:"soft",size:1,get disabled(){return e(X)},children:(r,o)=>{var R=V();Le(()=>be(R,(N(),W(()=>N()?N().path:"Rules")))),n(r,R)},$$slots:{default:!0,iconLeft:(r,o)=>{xe(r,{slot:"iconLeft"})},iconRight:(r,o)=>{Ge(r,{slot:"iconRight"})}}})},$$slots:{default:!0},$$legacy:!0}),k=>g(D,k),()=>e(D))},$$slots:{default:!0}});var H=se(p,2);q.Content(H,{side:"bottom",align:"start",children:(T,ve)=>{var _=rt(),j=b(_);Ve(j,1,i,Je,(r,o,R)=>{const O=A(()=>y()===R);q.Item(r,{onSelect:()=>function(G){L.set(G),oe()(G),e(B)()}(e(o)),get highlight(){return e(O)},children:(G,S)=>{ot(G,{get filepath(){return e(o),W(()=>e(o).path)}})},$$slots:{default:!0}})});var k=se(j,2),C=r=>{var o=nt(),R=ne(o);q.Separator(R,{});var O=se(R,2);q.Label(O,{children:(G,S)=>{Ie(G,{size:1,color:"neutral",children:(Oe,vt)=>{var Ce=V();Le(Ae=>be(Ce,Ae),[()=>(i(),y(),W(()=>`Move to ${i()[y()].path}`))],A),n(Oe,Ce)},$$slots:{default:!0}})},$$slots:{default:!0}}),n(r,o)};te(k,r=>{y(),i(),W(()=>y()!==void 0&&i()[y()])&&r(C)}),n(T,_)},$$slots:{default:!0}}),n(a,f)},$$slots:{default:!0},$$legacy:!0})},t=v=>{const a=A(()=>(pe(re),W(()=>[re.Hover])));Me(Se(v,{get content(){return e(Z)},get triggerOn(){return e(a)},side:"top",children:(m,f)=>{Fe(m,{color:"neutral",variant:"soft",size:1,disabled:!0,children:(p,H)=>{var T=V("Rules");n(p,T)},$$slots:{default:!0,iconLeft:(p,H)=>{xe(p,{slot:"iconLeft"})},iconRight:(p,H)=>{Ge(p,{slot:"iconRight"})}}})},$$slots:{default:!0},$$legacy:!0}),m=>g(D,m),()=>e(D))};te(I,v=>{e(M)?v(ue):v(t,!1)}),n(d,u)};te(Y,d=>{P(w,"$loading",$)||d(Q)}),n(K,c),$e(),E()}var ct=J('<div slot="iconLeft" class="c-move-text-btn__left_icon svelte-1yddhs6"><!></div>'),dt=J('<div class="l-file-controls svelte-1yddhs6" slot="header"><div class="l-file-controls-left svelte-1yddhs6"><div class="c-move-text-btn svelte-1yddhs6"><!></div> <div class="c-move-text-btn svelte-1yddhs6"><!></div></div> <div class="l-file-controls-right svelte-1yddhs6"><!></div></div>'),ut=J('<div class="c-memories-container svelte-1vchs21"><!></div>');Pe(function(K,z){fe(z,!1);const[$,E]=Ee(),i=()=>P(M,"$editorContent",$),N=()=>P(X,"$editorPath",$),y=new we(U),M=ee(null),X=ee(null),Z={handleMessageFromExtension(l){const s=l.data;if(s&&s.type===ye.loadFile){if(s.data.content!==void 0){const w=s.data.content.replace(/^\n+/,"");M.set(w)}s.data.pathName&&X.set(s.data.pathName)}return!0}};ze(()=>{y.registerConsumer(Z),U.postMessage({type:ye.memoriesLoaded})}),je(()=>{y.dispose()}),he();var oe=ut();We("message",qe,function(...l){var s;(s=y.onMessageFromExtension)==null||s.apply(this,l)});var ae=b(oe),le=l=>{(function(s,w){fe(w,!1);let L=ie(w,"text",12),F=ie(w,"path",8);const B=new we(U),D=new Te,x=new ke(U,B,D),de=new Ye(B);let c=h(""),Y=h(0),Q=h(0),d=h("neutral");const u=async()=>{F()&&x.saveFile({repoRoot:"",pathName:F(),content:L()})};async function I(t){if(!e(c))return;let v,a,m;const f=e(c).slice(0,20);if(t==="userGuidelines"?(v="Move Content to User Guidelines",a=`Are you sure you want to move the selected content "${f}" to your user guidelines?`,m=ge.userGuidelines):t==="augmentGuidelines"?(v="Move Content to Workspace Guidelines",a=`Are you sure you want to move the selected content "${f}" to workspace guidelines?`,m=ge.augmentGuidelines):(v="Move Content to Rule",a=`Are you sure you want to move the selected content "${f}" to rule file "${t.rule.path}"?`,m=ge.rules),!await x.openConfirmationModal({title:v,message:a,confirmButtonText:"Move",cancelButtonText:"Cancel"}))return;t==="userGuidelines"?x.updateUserGuidelines(e(c)):t==="augmentGuidelines"?x.updateWorkspaceGuidelines(e(c)):(await de.updateRuleContent({type:t.rule.type,path:t.rule.path,content:t.rule.content+`

`+e(c),description:t.rule.description}),x.showNotification({message:`Moved content "${f}" to rule file "${t.rule.path}"`,type:"info",openFileMessage:{repoRoot:"",pathName:`${et}/${tt}/${t.rule.path}`}}));const p=L().substring(0,e(Y))+L().substring(e(Q));return L(p),await u(),x.reportAgentSessionEvent({eventName:Qe.memoriesMove,conversationId:"",eventData:{memoriesMoveData:{target:m}}}),"success"}async function ue(t){await I({rule:t})}he(),st(s,{saveFunction:u,variant:"surface",size:2,resize:"vertical",class:"markdown-editor",get selectedText(){return e(c)},set selectedText(t){g(c,t)},get selectionStart(){return e(Y)},set selectionStart(t){g(Y,t)},get selectionEnd(){return e(Q)},set selectionEnd(t){g(Q,t)},get value(){return L()},set value(t){L(t)},$$slots:{header:(t,v)=>{var a=dt(),m=b(a),f=b(m),p=b(f);const H=A(()=>!e(c));Xe(p,{tooltip:{neutral:"Move highlighted text to user guidelines",success:"Text moved to user guidelines"},stateVariant:{success:"solid",neutral:"soft"},defaultColor:"neutral",onClick:()=>I("userGuidelines"),get disabled(){return e(H)},stickyColor:!1,persistOnTooltipClose:!0,replaceIconOnSuccess:!0,size:1,get state(){return e(d)},set state(C){g(d,C)},children:(C,r)=>{var o=V("User Guidelines");n(C,o)},$$slots:{default:!0,iconLeft:(C,r)=>{var o=ct(),R=b(o),O=S=>{Ze(S,{})},G=S=>{xe(S,{})};te(R,S=>{e(d)==="success"?S(O):S(G,!1)}),n(C,o)}},$$legacy:!0});var T=se(f,2),ve=b(T);const _=A(()=>!e(c));lt(ve,{onRuleSelected:ue,get disabled(){return e(_)}});var j=se(m,2),k=b(j);Ke(k,{size:1,get path(){return F()},variant:"soft",onOpenLocalFile:async()=>(x.openFile({repoRoot:"",pathName:F()}),"success"),$$slots:{text:(C,r)=>{Ie(C,{slot:"text",size:1,children:(o,R)=>{var O=V("Augment-Memories.md");n(o,O)},$$slots:{default:!0}})}}}),n(t,a)}},$$legacy:!0}),$e()})(l,{get text(){return i()},get path(){return N()}})},ce=l=>{var s=V("Loading memories...");n(l,s)};te(ae,l=>{i()!==null&&N()!==null?l(le):l(ce,!1)}),n(K,oe),$e(),E()},{target:document.getElementById("app")});
