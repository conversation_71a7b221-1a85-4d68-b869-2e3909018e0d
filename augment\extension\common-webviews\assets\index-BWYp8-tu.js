var bo=Object.defineProperty;var vo=(t,e,n)=>e in t?bo(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var c=(t,e,n)=>vo(t,typeof e!="symbol"?e+"":e,n);import{a as Eo}from"./async-messaging-BeBg25ZO.js";import{W as b,S as To,a as Wn,b as Ce,B as So,h as Io}from"./IconButtonAugment-BYROpfM6.js";import{R as le,P as V,C,b as Tn,I as Mt,a as G,E as wo}from"./message-broker-BVKpqRQ5.js";import{C as No}from"./types-CGlLNakm.js";import{n as Ro,f as Co,i as ko}from"./file-paths-BEJIrsZp.js";import{w as St,y as ea,aA as xo,f as ta,b as O,aH as Kn,A as he,C as N,v as na,u as Q,W as Se,_ as rt,D as pe,R as _e,ak as Ao,J,N as X,t as ae,O as at,aa as ot,a3 as Ee,I as ge,a1 as Me,Z as F,m as Ie,K as Pe,a4 as Mo,X as we,z as sa,l as B,a6 as Ve,G as Te,H as yt,L as je,M as _t,T as ra,F as Oe,aF as Ms,a2 as Zt,ba as Os,Y as aa,a as oa,b0 as Ds,al as ia,aD as Oo,B as Do,P as Fo}from"./SpinnerAugment-C3d3R_8C.js";import{a as Uo,c as Po,_ as Lo,i as is}from"./isObjectLike-DEzymPim.js";import{c as $o,e as Ht,f as qo,C as Ho,a as Gt,R as Go,b as Xe,g as Fs}from"./CardAugment-L1_52yiK.js";import{B as Bo,b as Vo}from"./BaseTextInput-hjj6nBZd.js";function ls(t,e){return!(t===null||typeof t!="object"||!("$typeName"in t)||typeof t.$typeName!="string")&&(e===void 0||e.typeName===t.$typeName)}var g;function Yo(){let t=0,e=0;for(let s=0;s<28;s+=7){let r=this.buf[this.pos++];if(t|=(127&r)<<s,!(128&r))return this.assertBounds(),[t,e]}let n=this.buf[this.pos++];if(t|=(15&n)<<28,e=(112&n)>>4,!(128&n))return this.assertBounds(),[t,e];for(let s=3;s<=31;s+=7){let r=this.buf[this.pos++];if(e|=(127&r)<<s,!(128&r))return this.assertBounds(),[t,e]}throw new Error("invalid varint")}function Sn(t,e,n){for(let a=0;a<28;a+=7){const o=t>>>a,i=!(!(o>>>7)&&e==0),l=255&(i?128|o:o);if(n.push(l),!i)return}const s=t>>>28&15|(7&e)<<4,r=!!(e>>3);if(n.push(255&(r?128|s:s)),r){for(let a=3;a<31;a+=7){const o=e>>>a,i=!!(o>>>7),l=255&(i?128|o:o);if(n.push(l),!i)return}n.push(e>>>31&1)}}(function(t){t[t.DOUBLE=1]="DOUBLE",t[t.FLOAT=2]="FLOAT",t[t.INT64=3]="INT64",t[t.UINT64=4]="UINT64",t[t.INT32=5]="INT32",t[t.FIXED64=6]="FIXED64",t[t.FIXED32=7]="FIXED32",t[t.BOOL=8]="BOOL",t[t.STRING=9]="STRING",t[t.BYTES=12]="BYTES",t[t.UINT32=13]="UINT32",t[t.SFIXED32=15]="SFIXED32",t[t.SFIXED64=16]="SFIXED64",t[t.SINT32=17]="SINT32",t[t.SINT64=18]="SINT64"})(g||(g={}));const Bt=4294967296;function Us(t){const e=t[0]==="-";e&&(t=t.slice(1));const n=1e6;let s=0,r=0;function a(o,i){const l=Number(t.slice(o,i));r*=n,s=s*n+l,s>=Bt&&(r+=s/Bt|0,s%=Bt)}return a(-24,-18),a(-18,-12),a(-12,-6),a(-6),e?la(s,r):us(s,r)}function Ps(t,e){if({lo:t,hi:e}=function(l,u){return{lo:l>>>0,hi:u>>>0}}(t,e),e<=2097151)return String(Bt*e+t);const n=16777215&(t>>>24|e<<8),s=e>>16&65535;let r=(16777215&t)+6777216*n+6710656*s,a=n+8147497*s,o=2*s;const i=1e7;return r>=i&&(a+=Math.floor(r/i),r%=i),a>=i&&(o+=Math.floor(a/i),a%=i),o.toString()+Ls(a)+Ls(r)}function us(t,e){return{lo:0|t,hi:0|e}}function la(t,e){return e=~e,t?t=1+~t:e+=1,us(t,e)}const Ls=t=>{const e=String(t);return"0000000".slice(e.length)+e};function $s(t,e){if(t>=0){for(;t>127;)e.push(127&t|128),t>>>=7;e.push(t)}else{for(let n=0;n<9;n++)e.push(127&t|128),t>>=7;e.push(1)}}function jo(){let t=this.buf[this.pos++],e=127&t;if(!(128&t))return this.assertBounds(),e;if(t=this.buf[this.pos++],e|=(127&t)<<7,!(128&t))return this.assertBounds(),e;if(t=this.buf[this.pos++],e|=(127&t)<<14,!(128&t))return this.assertBounds(),e;if(t=this.buf[this.pos++],e|=(127&t)<<21,!(128&t))return this.assertBounds(),e;t=this.buf[this.pos++],e|=(15&t)<<28;for(let n=5;128&t&&n<10;n++)t=this.buf[this.pos++];if(128&t)throw new Error("invalid varint");return this.assertBounds(),e>>>0}var qs={};const D=Wo();function Wo(){const t=new DataView(new ArrayBuffer(8));if(typeof BigInt=="function"&&typeof t.getBigInt64=="function"&&typeof t.getBigUint64=="function"&&typeof t.setBigInt64=="function"&&typeof t.setBigUint64=="function"&&(typeof process!="object"||typeof qs!="object"||qs.BUF_BIGINT_DISABLE!=="1")){const e=BigInt("-9223372036854775808"),n=BigInt("9223372036854775807"),s=BigInt("0"),r=BigInt("18446744073709551615");return{zero:BigInt(0),supported:!0,parse(a){const o=typeof a=="bigint"?a:BigInt(a);if(o>n||o<e)throw new Error(`invalid int64: ${a}`);return o},uParse(a){const o=typeof a=="bigint"?a:BigInt(a);if(o>r||o<s)throw new Error(`invalid uint64: ${a}`);return o},enc(a){return t.setBigInt64(0,this.parse(a),!0),{lo:t.getInt32(0,!0),hi:t.getInt32(4,!0)}},uEnc(a){return t.setBigInt64(0,this.uParse(a),!0),{lo:t.getInt32(0,!0),hi:t.getInt32(4,!0)}},dec:(a,o)=>(t.setInt32(0,a,!0),t.setInt32(4,o,!0),t.getBigInt64(0,!0)),uDec:(a,o)=>(t.setInt32(0,a,!0),t.setInt32(4,o,!0),t.getBigUint64(0,!0))}}return{zero:"0",supported:!1,parse:e=>(typeof e!="string"&&(e=e.toString()),Hs(e),e),uParse:e=>(typeof e!="string"&&(e=e.toString()),Gs(e),e),enc:e=>(typeof e!="string"&&(e=e.toString()),Hs(e),Us(e)),uEnc:e=>(typeof e!="string"&&(e=e.toString()),Gs(e),Us(e)),dec:(e,n)=>function(s,r){let a=us(s,r);const o=2147483648&a.hi;o&&(a=la(a.lo,a.hi));const i=Ps(a.lo,a.hi);return o?"-"+i:i}(e,n),uDec:(e,n)=>Ps(e,n)}}function Hs(t){if(!/^-?[0-9]+$/.test(t))throw new Error("invalid int64: "+t)}function Gs(t){if(!/^[0-9]+$/.test(t))throw new Error("invalid uint64: "+t)}function We(t,e){switch(t){case g.STRING:return"";case g.BOOL:return!1;case g.DOUBLE:case g.FLOAT:return 0;case g.INT64:case g.UINT64:case g.SFIXED64:case g.FIXED64:case g.SINT64:return e?"0":D.zero;case g.BYTES:return new Uint8Array(0);default:return 0}}const xe=Symbol.for("reflect unsafe local");function ua(t,e){const n=t[e.localName].case;return n===void 0?n:e.fields.find(s=>s.localName===n)}function Ko(t,e){const n=e.localName;if(e.oneof)return t[e.oneof.localName].case===n;if(e.presence!=2)return t[n]!==void 0&&Object.prototype.hasOwnProperty.call(t,n);switch(e.fieldKind){case"list":return t[n].length>0;case"map":return Object.keys(t[n]).length>0;case"scalar":return!function(s,r){switch(s){case g.BOOL:return r===!1;case g.STRING:return r==="";case g.BYTES:return r instanceof Uint8Array&&!r.byteLength;default:return r==0}}(e.scalar,t[n]);case"enum":return t[n]!==e.enum.values[0].number}throw new Error("message field with implicit presence")}function bt(t,e){return Object.prototype.hasOwnProperty.call(t,e)&&t[e]!==void 0}function ca(t,e){if(e.oneof){const n=t[e.oneof.localName];return n.case===e.localName?n.value:void 0}return t[e.localName]}function da(t,e,n){e.oneof?t[e.oneof.localName]={case:e.localName,value:n}:t[e.localName]=n}function qe(t){return t!==null&&typeof t=="object"&&!Array.isArray(t)}function cs(t,e){var n,s,r,a;if(qe(t)&&xe in t&&"add"in t&&"field"in t&&typeof t.field=="function"){if(e!==void 0){const o=e,i=t.field();return o.listKind==i.listKind&&o.scalar===i.scalar&&((n=o.message)===null||n===void 0?void 0:n.typeName)===((s=i.message)===null||s===void 0?void 0:s.typeName)&&((r=o.enum)===null||r===void 0?void 0:r.typeName)===((a=i.enum)===null||a===void 0?void 0:a.typeName)}return!0}return!1}function ds(t,e){var n,s,r,a;if(qe(t)&&xe in t&&"has"in t&&"field"in t&&typeof t.field=="function"){if(e!==void 0){const o=e,i=t.field();return o.mapKey===i.mapKey&&o.mapKind==i.mapKind&&o.scalar===i.scalar&&((n=o.message)===null||n===void 0?void 0:n.typeName)===((s=i.message)===null||s===void 0?void 0:s.typeName)&&((r=o.enum)===null||r===void 0?void 0:r.typeName)===((a=i.enum)===null||a===void 0?void 0:a.typeName)}return!0}return!1}function ms(t,e){return qe(t)&&xe in t&&"desc"in t&&qe(t.desc)&&t.desc.kind==="message"&&(e===void 0||t.desc.typeName==e.typeName)}function Ct(t){const e=t.fields[0];return ma(t.typeName)&&e!==void 0&&e.fieldKind=="scalar"&&e.name=="value"&&e.number==1}function ma(t){return t.startsWith("google.protobuf.")&&["DoubleValue","FloatValue","Int64Value","UInt64Value","Int32Value","UInt32Value","BoolValue","StringValue","BytesValue"].includes(t.substring(16))}const zo=999,Xo=998,Ot=2;function ye(t,e){if(ls(e,t))return e;const n=function(s){let r;if(function(a){switch(a.file.edition){case zo:return!1;case Xo:return!0;default:return a.fields.some(o=>o.presence!=Ot&&o.fieldKind!="message"&&!o.oneof)}}(s)){const a=Vs.get(s);let o,i;if(a)({prototype:o,members:i}=a);else{o={},i=new Set;for(const l of s.members)l.kind!="oneof"&&(l.fieldKind!="scalar"&&l.fieldKind!="enum"||l.presence!=Ot&&(i.add(l),o[l.localName]=In(l)));Vs.set(s,{prototype:o,members:i})}r=Object.create(o),r.$typeName=s.typeName;for(const l of s.members)if(!i.has(l)){if(l.kind=="field"&&(l.fieldKind=="message"||(l.fieldKind=="scalar"||l.fieldKind=="enum")&&l.presence!=Ot))continue;r[l.localName]=In(l)}}else{r={$typeName:s.typeName};for(const a of s.members)a.kind!="oneof"&&a.presence!=Ot||(r[a.localName]=In(a))}return r}(t);return e!==void 0&&function(s,r,a){for(const o of s.members){let i,l=a[o.localName];if(l!=null){if(o.kind=="oneof"){const u=ua(a,o);if(!u)continue;i=u,l=ca(a,u)}else i=o;switch(i.fieldKind){case"message":l=hs(i,l);break;case"scalar":l=ha(i,l);break;case"list":l=Zo(i,l);break;case"map":l=Jo(i,l)}da(r,i,l)}}}(t,n,e),n}function ha(t,e){return t.scalar==g.BYTES?ps(e):e}function Jo(t,e){if(qe(e)){if(t.scalar==g.BYTES)return Bs(e,ps);if(t.mapKind=="message")return Bs(e,n=>hs(t,n))}return e}function Zo(t,e){if(Array.isArray(e)){if(t.scalar==g.BYTES)return e.map(ps);if(t.listKind=="message")return e.map(n=>hs(t,n))}return e}function hs(t,e){if(t.fieldKind=="message"&&!t.oneof&&Ct(t.message))return ha(t.message.fields[0],e);if(qe(e)){if(t.message.typeName=="google.protobuf.Struct"&&t.parent.typeName!=="google.protobuf.Value")return e;if(!ls(e,t.message))return ye(t.message,e)}return e}function ps(t){return Array.isArray(t)?new Uint8Array(t):t}function Bs(t,e){const n={};for(const s of Object.entries(t))n[s[0]]=e(s[1]);return n}const Qo=Symbol(),Vs=new WeakMap;function In(t){if(t.kind=="oneof")return{case:void 0};if(t.fieldKind=="list")return[];if(t.fieldKind=="map")return{};if(t.fieldKind=="message")return Qo;const e=t.getDefaultValue();return e!==void 0?t.fieldKind=="scalar"&&t.longAsString?e.toString():e:t.fieldKind=="scalar"?We(t.scalar,t.longAsString):t.enum.values[0].number}const ei=["FieldValueInvalidError","FieldListRangeError","ForeignFieldError"];class ee extends Error{constructor(e,n,s="FieldValueInvalidError"){super(n),this.name=s,this.field=()=>e}}const wn=Symbol.for("@bufbuild/protobuf/text-encoding");function gs(){if(globalThis[wn]==null){const t=new globalThis.TextEncoder,e=new globalThis.TextDecoder;globalThis[wn]={encodeUtf8:n=>t.encode(n),decodeUtf8:n=>e.decode(n),checkUtf8(n){try{return encodeURIComponent(n),!0}catch{return!1}}}}return globalThis[wn]}var U;(function(t){t[t.Varint=0]="Varint",t[t.Bit64=1]="Bit64",t[t.LengthDelimited=2]="LengthDelimited",t[t.StartGroup=3]="StartGroup",t[t.EndGroup=4]="EndGroup",t[t.Bit32=5]="Bit32"})(U||(U={}));const pa=34028234663852886e22,ga=-34028234663852886e22,fa=4294967295,ya=2147483647,_a=-2147483648;class ba{constructor(e=gs().encodeUtf8){this.encodeUtf8=e,this.stack=[],this.chunks=[],this.buf=[]}finish(){this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]);let e=0;for(let r=0;r<this.chunks.length;r++)e+=this.chunks[r].length;let n=new Uint8Array(e),s=0;for(let r=0;r<this.chunks.length;r++)n.set(this.chunks[r],s),s+=this.chunks[r].length;return this.chunks=[],n}fork(){return this.stack.push({chunks:this.chunks,buf:this.buf}),this.chunks=[],this.buf=[],this}join(){let e=this.finish(),n=this.stack.pop();if(!n)throw new Error("invalid state, fork stack empty");return this.chunks=n.chunks,this.buf=n.buf,this.uint32(e.byteLength),this.raw(e)}tag(e,n){return this.uint32((e<<3|n)>>>0)}raw(e){return this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]),this.chunks.push(e),this}uint32(e){for(Ys(e);e>127;)this.buf.push(127&e|128),e>>>=7;return this.buf.push(e),this}int32(e){return Nn(e),$s(e,this.buf),this}bool(e){return this.buf.push(e?1:0),this}bytes(e){return this.uint32(e.byteLength),this.raw(e)}string(e){let n=this.encodeUtf8(e);return this.uint32(n.byteLength),this.raw(n)}float(e){(function(s){if(typeof s=="string"){const r=s;if(s=Number(s),Number.isNaN(s)&&r!=="NaN")throw new Error("invalid float32: "+r)}else if(typeof s!="number")throw new Error("invalid float32: "+typeof s);if(Number.isFinite(s)&&(s>pa||s<ga))throw new Error("invalid float32: "+s)})(e);let n=new Uint8Array(4);return new DataView(n.buffer).setFloat32(0,e,!0),this.raw(n)}double(e){let n=new Uint8Array(8);return new DataView(n.buffer).setFloat64(0,e,!0),this.raw(n)}fixed32(e){Ys(e);let n=new Uint8Array(4);return new DataView(n.buffer).setUint32(0,e,!0),this.raw(n)}sfixed32(e){Nn(e);let n=new Uint8Array(4);return new DataView(n.buffer).setInt32(0,e,!0),this.raw(n)}sint32(e){return Nn(e),$s(e=(e<<1^e>>31)>>>0,this.buf),this}sfixed64(e){let n=new Uint8Array(8),s=new DataView(n.buffer),r=D.enc(e);return s.setInt32(0,r.lo,!0),s.setInt32(4,r.hi,!0),this.raw(n)}fixed64(e){let n=new Uint8Array(8),s=new DataView(n.buffer),r=D.uEnc(e);return s.setInt32(0,r.lo,!0),s.setInt32(4,r.hi,!0),this.raw(n)}int64(e){let n=D.enc(e);return Sn(n.lo,n.hi,this.buf),this}sint64(e){const n=D.enc(e),s=n.hi>>31;return Sn(n.lo<<1^s,(n.hi<<1|n.lo>>>31)^s,this.buf),this}uint64(e){const n=D.uEnc(e);return Sn(n.lo,n.hi,this.buf),this}}class fs{constructor(e,n=gs().decodeUtf8){this.decodeUtf8=n,this.varint64=Yo,this.uint32=jo,this.buf=e,this.len=e.length,this.pos=0,this.view=new DataView(e.buffer,e.byteOffset,e.byteLength)}tag(){let e=this.uint32(),n=e>>>3,s=7&e;if(n<=0||s<0||s>5)throw new Error("illegal tag: field no "+n+" wire type "+s);return[n,s]}skip(e,n){let s=this.pos;switch(e){case U.Varint:for(;128&this.buf[this.pos++];);break;case U.Bit64:this.pos+=4;case U.Bit32:this.pos+=4;break;case U.LengthDelimited:let r=this.uint32();this.pos+=r;break;case U.StartGroup:for(;;){const[a,o]=this.tag();if(o===U.EndGroup){if(n!==void 0&&a!==n)throw new Error("invalid end group tag");break}this.skip(o,a)}break;default:throw new Error("cant skip wire type "+e)}return this.assertBounds(),this.buf.subarray(s,this.pos)}assertBounds(){if(this.pos>this.len)throw new RangeError("premature EOF")}int32(){return 0|this.uint32()}sint32(){let e=this.uint32();return e>>>1^-(1&e)}int64(){return D.dec(...this.varint64())}uint64(){return D.uDec(...this.varint64())}sint64(){let[e,n]=this.varint64(),s=-(1&e);return e=(e>>>1|(1&n)<<31)^s,n=n>>>1^s,D.dec(e,n)}bool(){let[e,n]=this.varint64();return e!==0||n!==0}fixed32(){return this.view.getUint32((this.pos+=4)-4,!0)}sfixed32(){return this.view.getInt32((this.pos+=4)-4,!0)}fixed64(){return D.uDec(this.sfixed32(),this.sfixed32())}sfixed64(){return D.dec(this.sfixed32(),this.sfixed32())}float(){return this.view.getFloat32((this.pos+=4)-4,!0)}double(){return this.view.getFloat64((this.pos+=8)-8,!0)}bytes(){let e=this.uint32(),n=this.pos;return this.pos+=e,this.assertBounds(),this.buf.subarray(n,n+e)}string(){return this.decodeUtf8(this.bytes())}}function Nn(t){if(typeof t=="string")t=Number(t);else if(typeof t!="number")throw new Error("invalid int32: "+typeof t);if(!Number.isInteger(t)||t>ya||t<_a)throw new Error("invalid int32: "+t)}function Ys(t){if(typeof t=="string")t=Number(t);else if(typeof t!="number")throw new Error("invalid uint32: "+typeof t);if(!Number.isInteger(t)||t>fa||t<0)throw new Error("invalid uint32: "+t)}function He(t,e){const n=t.fieldKind=="list"?cs(e,t):t.fieldKind=="map"?ds(e,t):ys(t,e);if(n===!0)return;let s;switch(t.fieldKind){case"list":s=`expected ${Ta(t)}, got ${$(e)}`;break;case"map":s=`expected ${Sa(t)}, got ${$(e)}`;break;default:s=Qt(t,e,n)}return new ee(t,s)}function js(t,e,n){const s=ys(t,n);if(s!==!0)return new ee(t,`list item #${e+1}: ${Qt(t,n,s)}`)}function ys(t,e){return t.scalar!==void 0?va(e,t.scalar):t.enum!==void 0?t.enum.open?Number.isInteger(e):t.enum.values.some(n=>n.number===e):ms(e,t.message)}function va(t,e){switch(e){case g.DOUBLE:return typeof t=="number";case g.FLOAT:return typeof t=="number"&&(!(!Number.isNaN(t)&&Number.isFinite(t))||!(t>pa||t<ga)||`${t.toFixed()} out of range`);case g.INT32:case g.SFIXED32:case g.SINT32:return!(typeof t!="number"||!Number.isInteger(t))&&(!(t>ya||t<_a)||`${t.toFixed()} out of range`);case g.FIXED32:case g.UINT32:return!(typeof t!="number"||!Number.isInteger(t))&&(!(t>fa||t<0)||`${t.toFixed()} out of range`);case g.BOOL:return typeof t=="boolean";case g.STRING:return typeof t=="string"&&(gs().checkUtf8(t)||"invalid UTF8");case g.BYTES:return t instanceof Uint8Array;case g.INT64:case g.SFIXED64:case g.SINT64:if(typeof t=="bigint"||typeof t=="number"||typeof t=="string"&&t.length>0)try{return D.parse(t),!0}catch{return`${t} out of range`}return!1;case g.FIXED64:case g.UINT64:if(typeof t=="bigint"||typeof t=="number"||typeof t=="string"&&t.length>0)try{return D.uParse(t),!0}catch{return`${t} out of range`}return!1}}function Qt(t,e,n){return n=typeof n=="string"?`: ${n}`:`, got ${$(e)}`,t.scalar!==void 0?`expected ${function(s){switch(s){case g.STRING:return"string";case g.BOOL:return"boolean";case g.INT64:case g.SINT64:case g.SFIXED64:return"bigint (int64)";case g.UINT64:case g.FIXED64:return"bigint (uint64)";case g.BYTES:return"Uint8Array";case g.DOUBLE:return"number (float64)";case g.FLOAT:return"number (float32)";case g.FIXED32:case g.UINT32:return"number (uint32)";case g.INT32:case g.SFIXED32:case g.SINT32:return"number (int32)"}}(t.scalar)}`+n:t.enum!==void 0?`expected ${t.enum.toString()}`+n:`expected ${Ea(t.message)}`+n}function $(t){switch(typeof t){case"object":return t===null?"null":t instanceof Uint8Array?`Uint8Array(${t.length})`:Array.isArray(t)?`Array(${t.length})`:cs(t)?Ta(t.field()):ds(t)?Sa(t.field()):ms(t)?Ea(t.desc):ls(t)?`message ${t.$typeName}`:"object";case"string":return t.length>30?"string":`"${t.split('"').join('\\"')}"`;case"boolean":case"number":return String(t);case"bigint":return String(t)+"n";default:return typeof t}}function Ea(t){return`ReflectMessage (${t.typeName})`}function Ta(t){switch(t.listKind){case"message":return`ReflectList (${t.message.toString()})`;case"enum":return`ReflectList (${t.enum.toString()})`;case"scalar":return`ReflectList (${g[t.scalar]})`}}function Sa(t){switch(t.mapKind){case"message":return`ReflectMap (${g[t.mapKey]}, ${t.message.toString()})`;case"enum":return`ReflectMap (${g[t.mapKey]}, ${t.enum.toString()})`;case"scalar":return`ReflectMap (${g[t.mapKey]}, ${g[t.scalar]})`}}function ue(t,e,n=!0){return new Ia(t,e,n)}class Ia{get sortedFields(){var e;return(e=this._sortedFields)!==null&&e!==void 0?e:this._sortedFields=this.desc.fields.concat().sort((n,s)=>n.number-s.number)}constructor(e,n,s=!0){this.lists=new Map,this.maps=new Map,this.check=s,this.desc=e,this.message=this[xe]=n??ye(e),this.fields=e.fields,this.oneofs=e.oneofs,this.members=e.members}findNumber(e){return this._fieldsByNumber||(this._fieldsByNumber=new Map(this.desc.fields.map(n=>[n.number,n]))),this._fieldsByNumber.get(e)}oneofCase(e){return dt(this.message,e),ua(this.message,e)}isSet(e){return dt(this.message,e),Ko(this.message,e)}clear(e){dt(this.message,e),function(n,s){const r=s.localName;if(s.oneof){const a=s.oneof.localName;n[a].case===r&&(n[a]={case:void 0})}else if(s.presence!=2)delete n[r];else switch(s.fieldKind){case"map":n[r]={};break;case"list":n[r]=[];break;case"enum":n[r]=s.enum.values[0].number;break;case"scalar":n[r]=We(s.scalar,s.longAsString)}}(this.message,e)}get(e){dt(this.message,e);const n=ca(this.message,e);switch(e.fieldKind){case"list":let s=this.lists.get(e);return s&&s[xe]===n||this.lists.set(e,s=new ti(e,n,this.check)),s;case"map":let r=this.maps.get(e);return r&&r[xe]===n||this.maps.set(e,r=new ni(e,n,this.check)),r;case"message":return bs(e,n,this.check);case"scalar":return n===void 0?We(e.scalar,!1):vs(e,n);case"enum":return n??e.enum.values[0].number}}set(e,n){if(dt(this.message,e),this.check){const r=He(e,n);if(r)throw r}let s;s=e.fieldKind=="message"?_s(e,n):ds(n)||cs(n)?n[xe]:Es(e,n),da(this.message,e,s)}getUnknown(){return this.message.$unknown}setUnknown(e){this.message.$unknown=e}}function dt(t,e){if(e.parent.typeName!==t.$typeName)throw new ee(e,`cannot use ${e.toString()} with message ${t.$typeName}`,"ForeignFieldError")}class ti{field(){return this._field}get size(){return this._arr.length}constructor(e,n,s){this._field=e,this._arr=this[xe]=n,this.check=s}get(e){const n=this._arr[e];return n===void 0?void 0:Rn(this._field,n,this.check)}set(e,n){if(e<0||e>=this._arr.length)throw new ee(this._field,`list item #${e+1}: out of range`);if(this.check){const s=js(this._field,e,n);if(s)throw s}this._arr[e]=Ws(this._field,n)}add(e){if(this.check){const n=js(this._field,this._arr.length,e);if(n)throw n}this._arr.push(Ws(this._field,e))}clear(){this._arr.splice(0,this._arr.length)}[Symbol.iterator](){return this.values()}keys(){return this._arr.keys()}*values(){for(const e of this._arr)yield Rn(this._field,e,this.check)}*entries(){for(let e=0;e<this._arr.length;e++)yield[e,Rn(this._field,this._arr[e],this.check)]}}class ni{constructor(e,n,s=!0){this.obj=this[xe]=n??{},this.check=s,this._field=e}field(){return this._field}set(e,n){if(this.check){const s=function(r,a,o){const i=va(a,r.mapKey);if(i!==!0)return new ee(r,`invalid map key: ${Qt({scalar:r.mapKey},a,i)}`);const l=ys(r,o);return l!==!0?new ee(r,`map entry ${$(a)}: ${Qt(r,o,l)}`):void 0}(this._field,e,n);if(s)throw s}return this.obj[Dt(e)]=function(s,r){return s.mapKind=="message"?_s(s,r):Es(s,r)}(this._field,n),this}delete(e){const n=Dt(e),s=Object.prototype.hasOwnProperty.call(this.obj,n);return s&&delete this.obj[n],s}clear(){for(const e of Object.keys(this.obj))delete this.obj[e]}get(e){let n=this.obj[Dt(e)];return n!==void 0&&(n=Cn(this._field,n,this.check)),n}has(e){return Object.prototype.hasOwnProperty.call(this.obj,Dt(e))}*keys(){for(const e of Object.keys(this.obj))yield Ks(e,this._field.mapKey)}*entries(){for(const e of Object.entries(this.obj))yield[Ks(e[0],this._field.mapKey),Cn(this._field,e[1],this.check)]}[Symbol.iterator](){return this.entries()}get size(){return Object.keys(this.obj).length}*values(){for(const e of Object.values(this.obj))yield Cn(this._field,e,this.check)}forEach(e,n){for(const s of this.entries())e.call(n,s[1],s[0],this)}}function _s(t,e){return ms(e)?ma(e.message.$typeName)&&!t.oneof&&t.fieldKind=="message"?e.message.value:e.desc.typeName=="google.protobuf.Struct"&&t.parent.typeName!="google.protobuf.Value"?Na(e.message):e.message:e}function bs(t,e,n){return e!==void 0&&(Ct(t.message)&&!t.oneof&&t.fieldKind=="message"?e={$typeName:t.message.typeName,value:vs(t.message.fields[0],e)}:t.message.typeName=="google.protobuf.Struct"&&t.parent.typeName!="google.protobuf.Value"&&qe(e)&&(e=wa(e))),new Ia(t.message,e,n)}function Ws(t,e){return t.listKind=="message"?_s(t,e):Es(t,e)}function Rn(t,e,n){return t.listKind=="message"?bs(t,e,n):vs(t,e)}function Cn(t,e,n){return t.mapKind=="message"?bs(t,e,n):e}function Dt(t){return typeof t=="string"||typeof t=="number"?t:String(t)}function Ks(t,e){switch(e){case g.STRING:return t;case g.INT32:case g.FIXED32:case g.UINT32:case g.SFIXED32:case g.SINT32:{const n=Number.parseInt(t);if(Number.isFinite(n))return n;break}case g.BOOL:switch(t){case"true":return!0;case"false":return!1}break;case g.UINT64:case g.FIXED64:try{return D.uParse(t)}catch{}break;default:try{return D.parse(t)}catch{}}return t}function vs(t,e){switch(t.scalar){case g.INT64:case g.SFIXED64:case g.SINT64:"longAsString"in t&&t.longAsString&&typeof e=="string"&&(e=D.parse(e));break;case g.FIXED64:case g.UINT64:"longAsString"in t&&t.longAsString&&typeof e=="string"&&(e=D.uParse(e))}return e}function Es(t,e){switch(t.scalar){case g.INT64:case g.SFIXED64:case g.SINT64:"longAsString"in t&&t.longAsString?e=String(e):typeof e!="string"&&typeof e!="number"||(e=D.parse(e));break;case g.FIXED64:case g.UINT64:"longAsString"in t&&t.longAsString?e=String(e):typeof e!="string"&&typeof e!="number"||(e=D.uParse(e))}return e}function wa(t){const e={$typeName:"google.protobuf.Struct",fields:{}};if(qe(t))for(const[n,s]of Object.entries(t))e.fields[n]=Ca(s);return e}function Na(t){const e={};for(const[n,s]of Object.entries(t.fields))e[n]=Ra(s);return e}function Ra(t){switch(t.kind.case){case"structValue":return Na(t.kind.value);case"listValue":return t.kind.value.values.map(Ra);case"nullValue":case void 0:return null;default:return t.kind.value}}function Ca(t){const e={$typeName:"google.protobuf.Value",kind:{case:void 0}};switch(typeof t){case"number":e.kind={case:"numberValue",value:t};break;case"string":e.kind={case:"stringValue",value:t};break;case"boolean":e.kind={case:"boolValue",value:t};break;case"object":if(t===null)e.kind={case:"nullValue",value:0};else if(Array.isArray(t)){const n={$typeName:"google.protobuf.ListValue",values:[]};if(Array.isArray(t))for(const s of t)n.values.push(Ca(s));e.kind={case:"listValue",value:n}}else e.kind={case:"structValue",value:wa(t)}}return e}function ka(t){const e=function(){if(!Je){Je=[];const l=xa("std");for(let u=0;u<l.length;u++)Je[l[u].charCodeAt(0)]=u;Je[45]=l.indexOf("+"),Je[95]=l.indexOf("/")}return Je}();let n=3*t.length/4;t[t.length-2]=="="?n-=2:t[t.length-1]=="="&&(n-=1);let s,r=new Uint8Array(n),a=0,o=0,i=0;for(let l=0;l<t.length;l++){if(s=e[t.charCodeAt(l)],s===void 0)switch(t[l]){case"=":o=0;case`
`:case"\r":case"	":case" ":continue;default:throw Error("invalid base64 string")}switch(o){case 0:i=s,o=1;break;case 1:r[a++]=i<<2|(48&s)>>4,i=s,o=2;break;case 2:r[a++]=(15&i)<<4|(60&s)>>2,i=s,o=3;break;case 3:r[a++]=(3&i)<<6|s,o=0}}if(o==1)throw Error("invalid base64 string");return r.subarray(0,a)}let Ft,zs,Je;function xa(t){return Ft||(Ft="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),zs=Ft.slice(0,-2).concat("-","_")),t=="url"?zs:Ft}function It(t){let e=!1;const n=[];for(let s=0;s<t.length;s++){let r=t.charAt(s);switch(r){case"_":e=!0;break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":n.push(r),e=!1;break;default:e&&(e=!1,r=r.toUpperCase()),n.push(r)}}return n.join("")}const si=new Set(["constructor","toString","toJSON","valueOf"]);function wt(t){return si.has(t)?t+"$":t}function Ts(t){for(const e of t.field)bt(e,"jsonName")||(e.jsonName=It(e.name));t.nestedType.forEach(Ts)}function ri(t,e){switch(t){case g.STRING:return e;case g.BYTES:{const n=function(s){const r=[],a={tail:s,c:"",next(){return this.tail.length!=0&&(this.c=this.tail[0],this.tail=this.tail.substring(1),!0)},take(o){if(this.tail.length>=o){const i=this.tail.substring(0,o);return this.tail=this.tail.substring(o),i}return!1}};for(;a.next();)if(a.c==="\\"){if(a.next())switch(a.c){case"\\":r.push(a.c.charCodeAt(0));break;case"b":r.push(8);break;case"f":r.push(12);break;case"n":r.push(10);break;case"r":r.push(13);break;case"t":r.push(9);break;case"v":r.push(11);break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":{const o=a.c,i=a.take(2);if(i===!1)return!1;const l=parseInt(o+i,8);if(Number.isNaN(l))return!1;r.push(l);break}case"x":{const o=a.c,i=a.take(2);if(i===!1)return!1;const l=parseInt(o+i,16);if(Number.isNaN(l))return!1;r.push(l);break}case"u":{const o=a.c,i=a.take(4);if(i===!1)return!1;const l=parseInt(o+i,16);if(Number.isNaN(l))return!1;const u=new Uint8Array(4);new DataView(u.buffer).setInt32(0,l,!0),r.push(u[0],u[1],u[2],u[3]);break}case"U":{const o=a.c,i=a.take(8);if(i===!1)return!1;const l=D.uEnc(o+i),u=new Uint8Array(8),d=new DataView(u.buffer);d.setInt32(0,l.lo,!0),d.setInt32(4,l.hi,!0),r.push(u[0],u[1],u[2],u[3],u[4],u[5],u[6],u[7]);break}}}else r.push(a.c.charCodeAt(0));return new Uint8Array(r)}(e);if(n===!1)throw new Error(`cannot parse ${g[t]} default value: ${e}`);return n}case g.INT64:case g.SFIXED64:case g.SINT64:return D.parse(e);case g.UINT64:case g.FIXED64:return D.uParse(e);case g.DOUBLE:case g.FLOAT:switch(e){case"inf":return Number.POSITIVE_INFINITY;case"-inf":return Number.NEGATIVE_INFINITY;case"nan":return Number.NaN;default:return parseFloat(e)}case g.BOOL:return e==="true";case g.INT32:case g.UINT32:case g.SINT32:case g.FIXED32:case g.SFIXED32:return parseInt(e,10)}}function*zn(t){switch(t.kind){case"file":for(const e of t.messages)yield e,yield*zn(e);yield*t.enums,yield*t.services,yield*t.extensions;break;case"message":for(const e of t.nestedMessages)yield e,yield*zn(e);yield*t.nestedEnums,yield*t.nestedExtensions}}function Aa(...t){const e=function(){const n=new Map,s=new Map,r=new Map;return{kind:"registry",types:n,extendees:s,[Symbol.iterator]:()=>n.values(),get files(){return r.values()},addFile(a,o,i){if(r.set(a.proto.name,a),!o)for(const l of zn(a))this.add(l);if(i)for(const l of a.dependencies)this.addFile(l,o,i)},add(a){if(a.kind=="extension"){let o=s.get(a.extendee.typeName);o||s.set(a.extendee.typeName,o=new Map),o.set(a.number,a)}n.set(a.typeName,a)},get:a=>n.get(a),getFile:a=>r.get(a),getMessage(a){const o=n.get(a);return(o==null?void 0:o.kind)=="message"?o:void 0},getEnum(a){const o=n.get(a);return(o==null?void 0:o.kind)=="enum"?o:void 0},getExtension(a){const o=n.get(a);return(o==null?void 0:o.kind)=="extension"?o:void 0},getExtensionFor(a,o){var i;return(i=s.get(a.typeName))===null||i===void 0?void 0:i.get(o)},getService(a){const o=n.get(a);return(o==null?void 0:o.kind)=="service"?o:void 0}}}();if(!t.length)return e;if("$typeName"in t[0]&&t[0].$typeName=="google.protobuf.FileDescriptorSet"){for(const n of t[0].file)Zs(n,e);return e}if("$typeName"in t[0]){let a=function(o){const i=[];for(const l of o.dependency){if(e.getFile(l)!=null||r.has(l))continue;const u=s(l);if(!u)throw new Error(`Unable to resolve ${l}, imported by ${o.name}`);"kind"in u?e.addFile(u,!1,!0):(r.add(u.name),i.push(u))}return i.concat(...i.map(a))};const n=t[0],s=t[1],r=new Set;for(const o of[n,...a(n)].reverse())Zs(o,e)}else for(const n of t)for(const s of n.files)e.addFile(s);return e}const ai=998,oi=999,ii=9,Vt=10,pt=11,li=12,Xs=14,Xn=3,ui=2,Js=1,ci=0,di=1,mi=2,hi=3,pi=1,gi=2,fi=1,Ma={998:{fieldPresence:1,enumType:2,repeatedFieldEncoding:2,utf8Validation:3,messageEncoding:1,jsonFormat:2,enforceNamingStyle:2},999:{fieldPresence:2,enumType:1,repeatedFieldEncoding:1,utf8Validation:2,messageEncoding:1,jsonFormat:1,enforceNamingStyle:2},1e3:{fieldPresence:1,enumType:1,repeatedFieldEncoding:1,utf8Validation:2,messageEncoding:1,jsonFormat:1,enforceNamingStyle:2}};function Zs(t,e){var n,s;const r={kind:"file",proto:t,deprecated:(s=(n=t.options)===null||n===void 0?void 0:n.deprecated)!==null&&s!==void 0&&s,edition:bi(t),name:t.name.replace(/\.proto$/,""),dependencies:vi(t,e),enums:[],messages:[],extensions:[],services:[],toString:()=>`file ${t.name}`},a=new Map,o={get:i=>a.get(i),add(i){var l;fe(((l=i.proto.options)===null||l===void 0?void 0:l.mapEntry)===!0),a.set(i.typeName,i)}};for(const i of t.enumType)Oa(i,r,void 0,e);for(const i of t.messageType)Da(i,r,void 0,e,o);for(const i of t.service)yi(i,r,e);Jn(r,e);for(const i of a.values())Zn(i,e,o);for(const i of r.messages)Zn(i,e,o),Jn(i,e);e.addFile(r,!0)}function Jn(t,e){switch(t.kind){case"file":for(const n of t.proto.extension){const s=Qn(n,t,e);t.extensions.push(s),e.add(s)}break;case"message":for(const n of t.proto.extension){const s=Qn(n,t,e);t.nestedExtensions.push(s),e.add(s)}for(const n of t.nestedMessages)Jn(n,e)}}function Zn(t,e,n){const s=t.proto.oneofDecl.map(a=>function(o,i){return{kind:"oneof",proto:o,deprecated:!1,parent:i,fields:[],name:o.name,localName:wt(It(o.name)),toString(){return`oneof ${i.typeName}.${this.name}`}}}(a,t)),r=new Set;for(const a of t.proto.field){const o=Ei(a,s),i=Qn(a,t,e,o,n);t.fields.push(i),t.field[i.localName]=i,o===void 0?t.members.push(i):(o.fields.push(i),r.has(o)||(r.add(o),t.members.push(o)))}for(const a of s.filter(o=>r.has(o)))t.oneofs.push(a);for(const a of t.nestedMessages)Zn(a,e,n)}function Oa(t,e,n,s){var r,a,o,i,l;const u=function(m,p){const h=(f=m,(f.substring(0,1)+f.substring(1).replace(/[A-Z]/g,_=>"_"+_)).toLowerCase()+"_");var f;for(const _ of p){if(!_.name.toLowerCase().startsWith(h))return;const y=_.name.substring(h.length);if(y.length==0||/^\d/.test(y))return}return h}(t.name,t.value),d={kind:"enum",proto:t,deprecated:(a=(r=t.options)===null||r===void 0?void 0:r.deprecated)!==null&&a!==void 0&&a,file:e,parent:n,open:!0,name:t.name,typeName:yn(t,n,e),value:{},values:[],sharedPrefix:u,toString(){return`enum ${this.typeName}`}};d.open=function(m){var p;return fi==lt("enumType",{proto:m.proto,parent:(p=m.parent)!==null&&p!==void 0?p:m.file})}(d),s.add(d);for(const m of t.value){const p=m.name;d.values.push(d.value[m.number]={kind:"enum_value",proto:m,deprecated:(i=(o=t.options)===null||o===void 0?void 0:o.deprecated)!==null&&i!==void 0&&i,parent:d,name:p,localName:wt(u==null?p:p.substring(u.length)),number:m.number,toString:()=>`enum value ${d.typeName}.${p}`})}((l=n==null?void 0:n.nestedEnums)!==null&&l!==void 0?l:e.enums).push(d)}function Da(t,e,n,s,r){var a,o,i,l;const u={kind:"message",proto:t,deprecated:(o=(a=t.options)===null||a===void 0?void 0:a.deprecated)!==null&&o!==void 0&&o,file:e,parent:n,name:t.name,typeName:yn(t,n,e),fields:[],field:{},oneofs:[],members:[],nestedEnums:[],nestedMessages:[],nestedExtensions:[],toString(){return`message ${this.typeName}`}};((i=t.options)===null||i===void 0?void 0:i.mapEntry)===!0?r.add(u):(((l=n==null?void 0:n.nestedMessages)!==null&&l!==void 0?l:e.messages).push(u),s.add(u));for(const d of t.enumType)Oa(d,e,u,s);for(const d of t.nestedType)Da(d,e,u,s,r)}function yi(t,e,n){var s,r;const a={kind:"service",proto:t,deprecated:(r=(s=t.options)===null||s===void 0?void 0:s.deprecated)!==null&&r!==void 0&&r,file:e,name:t.name,typeName:yn(t,void 0,e),methods:[],method:{},toString(){return`service ${this.typeName}`}};e.services.push(a),n.add(a);for(const o of t.method){const i=_i(o,a,n);a.methods.push(i),a.method[i.localName]=i}}function _i(t,e,n){var s,r,a,o;let i;i=t.clientStreaming&&t.serverStreaming?"bidi_streaming":t.clientStreaming?"client_streaming":t.serverStreaming?"server_streaming":"unary";const l=n.getMessage(ke(t.inputType)),u=n.getMessage(ke(t.outputType));fe(l,`invalid MethodDescriptorProto: input_type ${t.inputType} not found`),fe(u,`invalid MethodDescriptorProto: output_type ${t.inputType} not found`);const d=t.name;return{kind:"rpc",proto:t,deprecated:(r=(s=t.options)===null||s===void 0?void 0:s.deprecated)!==null&&r!==void 0&&r,parent:e,name:d,localName:wt(d.length?wt(d[0].toLowerCase()+d.substring(1)):d),methodKind:i,input:l,output:u,idempotency:(o=(a=t.options)===null||a===void 0?void 0:a.idempotencyLevel)!==null&&o!==void 0?o:ci,toString:()=>`rpc ${e.typeName}.${d}`}}function Qn(t,e,n,s,r){var a,o,i;const l=r===void 0,u={kind:"field",proto:t,deprecated:(o=(a=t.options)===null||a===void 0?void 0:a.deprecated)!==null&&o!==void 0&&o,name:t.name,number:t.number,scalar:void 0,message:void 0,enum:void 0,presence:Ti(t,s,l,e),listKind:void 0,mapKind:void 0,mapKey:void 0,delimitedEncoding:void 0,packed:void 0,longAsString:!1,getDefaultValue:void 0};if(l){const h=e.kind=="file"?e:e.file,f=e.kind=="file"?void 0:e,_=yn(t,f,h);u.kind="extension",u.file=h,u.parent=f,u.oneof=void 0,u.typeName=_,u.jsonName=`[${_}]`,u.toString=()=>`extension ${_}`;const y=n.getMessage(ke(t.extendee));fe(y,`invalid FieldDescriptorProto: extendee ${t.extendee} not found`),u.extendee=y}else{const h=e;fe(h.kind=="message"),u.parent=h,u.oneof=s,u.localName=s?It(t.name):wt(It(t.name)),u.jsonName=t.jsonName,u.toString=()=>`field ${h.typeName}.${t.name}`}const d=t.label,m=t.type,p=(i=t.options)===null||i===void 0?void 0:i.jstype;if(d===Xn){const h=m==pt?r==null?void 0:r.get(ke(t.typeName)):void 0;if(h){u.fieldKind="map";const{key:f,value:_}=function(y){const v=y.fields.find(T=>T.number===1),E=y.fields.find(T=>T.number===2);return fe(v&&v.fieldKind=="scalar"&&v.scalar!=g.BYTES&&v.scalar!=g.FLOAT&&v.scalar!=g.DOUBLE&&E&&E.fieldKind!="list"&&E.fieldKind!="map"),{key:v,value:E}}(h);return u.mapKey=f.scalar,u.mapKind=_.fieldKind,u.message=_.message,u.delimitedEncoding=!1,u.enum=_.enum,u.scalar=_.scalar,u}switch(u.fieldKind="list",m){case pt:case Vt:u.listKind="message",u.message=n.getMessage(ke(t.typeName)),fe(u.message),u.delimitedEncoding=Qs(t,e);break;case Xs:u.listKind="enum",u.enum=n.getEnum(ke(t.typeName)),fe(u.enum);break;default:u.listKind="scalar",u.scalar=m,u.longAsString=p==Js}return u.packed=function(f,_){if(f.label!=Xn)return!1;switch(f.type){case ii:case li:case Vt:case pt:return!1}const y=f.options;return y&&bt(y,"packed")?y.packed:pi==lt("repeatedFieldEncoding",{proto:f,parent:_})}(t,e),u}switch(m){case pt:case Vt:u.fieldKind="message",u.message=n.getMessage(ke(t.typeName)),fe(u.message,`invalid FieldDescriptorProto: type_name ${t.typeName} not found`),u.delimitedEncoding=Qs(t,e),u.getDefaultValue=()=>{};break;case Xs:{const h=n.getEnum(ke(t.typeName));fe(h!==void 0,`invalid FieldDescriptorProto: type_name ${t.typeName} not found`),u.fieldKind="enum",u.enum=n.getEnum(ke(t.typeName)),u.getDefaultValue=()=>bt(t,"defaultValue")?function(f,_){const y=f.values.find(v=>v.name===_);if(!y)throw new Error(`cannot parse ${f} default value: ${_}`);return y.number}(h,t.defaultValue):void 0;break}default:u.fieldKind="scalar",u.scalar=m,u.longAsString=p==Js,u.getDefaultValue=()=>bt(t,"defaultValue")?ri(m,t.defaultValue):void 0}return u}function bi(t){switch(t.syntax){case"":case"proto2":return ai;case"proto3":return oi;case"editions":if(t.edition in Ma)return t.edition;throw new Error(`${t.name}: unsupported edition`);default:throw new Error(`${t.name}: unsupported syntax "${t.syntax}"`)}}function vi(t,e){return t.dependency.map(n=>{const s=e.getFile(n);if(!s)throw new Error(`Cannot find ${n}, imported by ${t.name}`);return s})}function yn(t,e,n){let s;return s=e?`${e.typeName}.${t.name}`:n.proto.package.length>0?`${n.proto.package}.${t.name}`:`${t.name}`,s}function ke(t){return t.startsWith(".")?t.substring(1):t}function Ei(t,e){if(!bt(t,"oneofIndex")||t.proto3Optional)return;const n=e[t.oneofIndex];return fe(n,`invalid FieldDescriptorProto: oneof #${t.oneofIndex} for field #${t.number} not found`),n}function Ti(t,e,n,s){return t.label==ui?hi:t.label==Xn?mi:e||t.proto3Optional||t.type==pt||n?di:lt("fieldPresence",{proto:t,parent:s})}function Qs(t,e){return t.type==Vt||gi==lt("messageEncoding",{proto:t,parent:e})}function lt(t,e){var n,s;const r=(n=e.proto.options)===null||n===void 0?void 0:n.features;if(r){const a=r[t];if(a!=0)return a}if("kind"in e){if(e.kind=="message")return lt(t,(s=e.parent)!==null&&s!==void 0?s:e.file);const a=Ma[e.edition];if(!a)throw new Error(`feature default for edition ${e.edition} not found`);return a[t]}return lt(t,e.parent)}function fe(t,e){if(!t)throw new Error(e)}function Si(t){const e=function(n){return Object.assign(Object.create({syntax:"",edition:0}),Object.assign(Object.assign({$typeName:"google.protobuf.FileDescriptorProto",dependency:[],publicDependency:[],weakDependency:[],service:[],extension:[]},n),{messageType:n.messageType.map(Fa),enumType:n.enumType.map(Ua)}))}(t);return e.messageType.forEach(Ts),Aa(e,()=>{}).getFile(e.name)}function Fa(t){var e,n,s,r,a,o,i,l;return{$typeName:"google.protobuf.DescriptorProto",name:t.name,field:(n=(e=t.field)===null||e===void 0?void 0:e.map(Ii))!==null&&n!==void 0?n:[],extension:[],nestedType:(r=(s=t.nestedType)===null||s===void 0?void 0:s.map(Fa))!==null&&r!==void 0?r:[],enumType:(o=(a=t.enumType)===null||a===void 0?void 0:a.map(Ua))!==null&&o!==void 0?o:[],extensionRange:(l=(i=t.extensionRange)===null||i===void 0?void 0:i.map(u=>Object.assign({$typeName:"google.protobuf.DescriptorProto.ExtensionRange"},u)))!==null&&l!==void 0?l:[],oneofDecl:[],reservedRange:[],reservedName:[]}}function Ii(t){return Object.assign(Object.create({label:1,typeName:"",extendee:"",defaultValue:"",oneofIndex:0,jsonName:"",proto3Optional:!1}),Object.assign(Object.assign({$typeName:"google.protobuf.FieldDescriptorProto"},t),{options:t.options?wi(t.options):void 0}))}function wi(t){var e,n,s;return Object.assign(Object.create({ctype:0,packed:!1,jstype:0,lazy:!1,unverifiedLazy:!1,deprecated:!1,weak:!1,debugRedact:!1,retention:0}),Object.assign(Object.assign({$typeName:"google.protobuf.FieldOptions"},t),{targets:(e=t.targets)!==null&&e!==void 0?e:[],editionDefaults:(s=(n=t.editionDefaults)===null||n===void 0?void 0:n.map(a=>Object.assign({$typeName:"google.protobuf.FieldOptions.EditionDefault"},a)))!==null&&s!==void 0?s:[],uninterpretedOption:[]}))}function Ua(t){return{$typeName:"google.protobuf.EnumDescriptorProto",name:t.name,reservedName:[],reservedRange:[],value:t.value.map(e=>Object.assign({$typeName:"google.protobuf.EnumValueDescriptorProto"},e))}}function kt(t,e,...n){return n.reduce((s,r)=>s.nestedMessages[r],t.messages[e])}const Ni=kt(Si({name:"google/protobuf/descriptor.proto",package:"google.protobuf",messageType:[{name:"FileDescriptorSet",field:[{name:"file",number:1,type:11,label:3,typeName:".google.protobuf.FileDescriptorProto"}],extensionRange:[{start:536e6,end:536000001}]},{name:"FileDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"package",number:2,type:9,label:1},{name:"dependency",number:3,type:9,label:3},{name:"public_dependency",number:10,type:5,label:3},{name:"weak_dependency",number:11,type:5,label:3},{name:"message_type",number:4,type:11,label:3,typeName:".google.protobuf.DescriptorProto"},{name:"enum_type",number:5,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto"},{name:"service",number:6,type:11,label:3,typeName:".google.protobuf.ServiceDescriptorProto"},{name:"extension",number:7,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"options",number:8,type:11,label:1,typeName:".google.protobuf.FileOptions"},{name:"source_code_info",number:9,type:11,label:1,typeName:".google.protobuf.SourceCodeInfo"},{name:"syntax",number:12,type:9,label:1},{name:"edition",number:14,type:14,label:1,typeName:".google.protobuf.Edition"}]},{name:"DescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"field",number:2,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"extension",number:6,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"nested_type",number:3,type:11,label:3,typeName:".google.protobuf.DescriptorProto"},{name:"enum_type",number:4,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto"},{name:"extension_range",number:5,type:11,label:3,typeName:".google.protobuf.DescriptorProto.ExtensionRange"},{name:"oneof_decl",number:8,type:11,label:3,typeName:".google.protobuf.OneofDescriptorProto"},{name:"options",number:7,type:11,label:1,typeName:".google.protobuf.MessageOptions"},{name:"reserved_range",number:9,type:11,label:3,typeName:".google.protobuf.DescriptorProto.ReservedRange"},{name:"reserved_name",number:10,type:9,label:3}],nestedType:[{name:"ExtensionRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.ExtensionRangeOptions"}]},{name:"ReservedRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1}]}]},{name:"ExtensionRangeOptions",field:[{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"},{name:"declaration",number:2,type:11,label:3,typeName:".google.protobuf.ExtensionRangeOptions.Declaration",options:{retention:2}},{name:"features",number:50,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"verification",number:3,type:14,label:1,typeName:".google.protobuf.ExtensionRangeOptions.VerificationState",defaultValue:"UNVERIFIED",options:{retention:2}}],nestedType:[{name:"Declaration",field:[{name:"number",number:1,type:5,label:1},{name:"full_name",number:2,type:9,label:1},{name:"type",number:3,type:9,label:1},{name:"reserved",number:5,type:8,label:1},{name:"repeated",number:6,type:8,label:1}]}],enumType:[{name:"VerificationState",value:[{name:"DECLARATION",number:0},{name:"UNVERIFIED",number:1}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"FieldDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"number",number:3,type:5,label:1},{name:"label",number:4,type:14,label:1,typeName:".google.protobuf.FieldDescriptorProto.Label"},{name:"type",number:5,type:14,label:1,typeName:".google.protobuf.FieldDescriptorProto.Type"},{name:"type_name",number:6,type:9,label:1},{name:"extendee",number:2,type:9,label:1},{name:"default_value",number:7,type:9,label:1},{name:"oneof_index",number:9,type:5,label:1},{name:"json_name",number:10,type:9,label:1},{name:"options",number:8,type:11,label:1,typeName:".google.protobuf.FieldOptions"},{name:"proto3_optional",number:17,type:8,label:1}],enumType:[{name:"Type",value:[{name:"TYPE_DOUBLE",number:1},{name:"TYPE_FLOAT",number:2},{name:"TYPE_INT64",number:3},{name:"TYPE_UINT64",number:4},{name:"TYPE_INT32",number:5},{name:"TYPE_FIXED64",number:6},{name:"TYPE_FIXED32",number:7},{name:"TYPE_BOOL",number:8},{name:"TYPE_STRING",number:9},{name:"TYPE_GROUP",number:10},{name:"TYPE_MESSAGE",number:11},{name:"TYPE_BYTES",number:12},{name:"TYPE_UINT32",number:13},{name:"TYPE_ENUM",number:14},{name:"TYPE_SFIXED32",number:15},{name:"TYPE_SFIXED64",number:16},{name:"TYPE_SINT32",number:17},{name:"TYPE_SINT64",number:18}]},{name:"Label",value:[{name:"LABEL_OPTIONAL",number:1},{name:"LABEL_REPEATED",number:3},{name:"LABEL_REQUIRED",number:2}]}]},{name:"OneofDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"options",number:2,type:11,label:1,typeName:".google.protobuf.OneofOptions"}]},{name:"EnumDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"value",number:2,type:11,label:3,typeName:".google.protobuf.EnumValueDescriptorProto"},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.EnumOptions"},{name:"reserved_range",number:4,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto.EnumReservedRange"},{name:"reserved_name",number:5,type:9,label:3}],nestedType:[{name:"EnumReservedRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1}]}]},{name:"EnumValueDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"number",number:2,type:5,label:1},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.EnumValueOptions"}]},{name:"ServiceDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"method",number:2,type:11,label:3,typeName:".google.protobuf.MethodDescriptorProto"},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.ServiceOptions"}]},{name:"MethodDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"input_type",number:2,type:9,label:1},{name:"output_type",number:3,type:9,label:1},{name:"options",number:4,type:11,label:1,typeName:".google.protobuf.MethodOptions"},{name:"client_streaming",number:5,type:8,label:1,defaultValue:"false"},{name:"server_streaming",number:6,type:8,label:1,defaultValue:"false"}]},{name:"FileOptions",field:[{name:"java_package",number:1,type:9,label:1},{name:"java_outer_classname",number:8,type:9,label:1},{name:"java_multiple_files",number:10,type:8,label:1,defaultValue:"false"},{name:"java_generate_equals_and_hash",number:20,type:8,label:1,options:{deprecated:!0}},{name:"java_string_check_utf8",number:27,type:8,label:1,defaultValue:"false"},{name:"optimize_for",number:9,type:14,label:1,typeName:".google.protobuf.FileOptions.OptimizeMode",defaultValue:"SPEED"},{name:"go_package",number:11,type:9,label:1},{name:"cc_generic_services",number:16,type:8,label:1,defaultValue:"false"},{name:"java_generic_services",number:17,type:8,label:1,defaultValue:"false"},{name:"py_generic_services",number:18,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:23,type:8,label:1,defaultValue:"false"},{name:"cc_enable_arenas",number:31,type:8,label:1,defaultValue:"true"},{name:"objc_class_prefix",number:36,type:9,label:1},{name:"csharp_namespace",number:37,type:9,label:1},{name:"swift_prefix",number:39,type:9,label:1},{name:"php_class_prefix",number:40,type:9,label:1},{name:"php_namespace",number:41,type:9,label:1},{name:"php_metadata_namespace",number:44,type:9,label:1},{name:"ruby_package",number:45,type:9,label:1},{name:"features",number:50,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],enumType:[{name:"OptimizeMode",value:[{name:"SPEED",number:1},{name:"CODE_SIZE",number:2},{name:"LITE_RUNTIME",number:3}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"MessageOptions",field:[{name:"message_set_wire_format",number:1,type:8,label:1,defaultValue:"false"},{name:"no_standard_descriptor_accessor",number:2,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"map_entry",number:7,type:8,label:1},{name:"deprecated_legacy_json_field_conflicts",number:11,type:8,label:1,options:{deprecated:!0}},{name:"features",number:12,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"FieldOptions",field:[{name:"ctype",number:1,type:14,label:1,typeName:".google.protobuf.FieldOptions.CType",defaultValue:"STRING"},{name:"packed",number:2,type:8,label:1},{name:"jstype",number:6,type:14,label:1,typeName:".google.protobuf.FieldOptions.JSType",defaultValue:"JS_NORMAL"},{name:"lazy",number:5,type:8,label:1,defaultValue:"false"},{name:"unverified_lazy",number:15,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"weak",number:10,type:8,label:1,defaultValue:"false"},{name:"debug_redact",number:16,type:8,label:1,defaultValue:"false"},{name:"retention",number:17,type:14,label:1,typeName:".google.protobuf.FieldOptions.OptionRetention"},{name:"targets",number:19,type:14,label:3,typeName:".google.protobuf.FieldOptions.OptionTargetType"},{name:"edition_defaults",number:20,type:11,label:3,typeName:".google.protobuf.FieldOptions.EditionDefault"},{name:"features",number:21,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"feature_support",number:22,type:11,label:1,typeName:".google.protobuf.FieldOptions.FeatureSupport"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],nestedType:[{name:"EditionDefault",field:[{name:"edition",number:3,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"value",number:2,type:9,label:1}]},{name:"FeatureSupport",field:[{name:"edition_introduced",number:1,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"edition_deprecated",number:2,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"deprecation_warning",number:3,type:9,label:1},{name:"edition_removed",number:4,type:14,label:1,typeName:".google.protobuf.Edition"}]}],enumType:[{name:"CType",value:[{name:"STRING",number:0},{name:"CORD",number:1},{name:"STRING_PIECE",number:2}]},{name:"JSType",value:[{name:"JS_NORMAL",number:0},{name:"JS_STRING",number:1},{name:"JS_NUMBER",number:2}]},{name:"OptionRetention",value:[{name:"RETENTION_UNKNOWN",number:0},{name:"RETENTION_RUNTIME",number:1},{name:"RETENTION_SOURCE",number:2}]},{name:"OptionTargetType",value:[{name:"TARGET_TYPE_UNKNOWN",number:0},{name:"TARGET_TYPE_FILE",number:1},{name:"TARGET_TYPE_EXTENSION_RANGE",number:2},{name:"TARGET_TYPE_MESSAGE",number:3},{name:"TARGET_TYPE_FIELD",number:4},{name:"TARGET_TYPE_ONEOF",number:5},{name:"TARGET_TYPE_ENUM",number:6},{name:"TARGET_TYPE_ENUM_ENTRY",number:7},{name:"TARGET_TYPE_SERVICE",number:8},{name:"TARGET_TYPE_METHOD",number:9}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"OneofOptions",field:[{name:"features",number:1,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"EnumOptions",field:[{name:"allow_alias",number:2,type:8,label:1},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"deprecated_legacy_json_field_conflicts",number:6,type:8,label:1,options:{deprecated:!0}},{name:"features",number:7,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"EnumValueOptions",field:[{name:"deprecated",number:1,type:8,label:1,defaultValue:"false"},{name:"features",number:2,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"debug_redact",number:3,type:8,label:1,defaultValue:"false"},{name:"feature_support",number:4,type:11,label:1,typeName:".google.protobuf.FieldOptions.FeatureSupport"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"ServiceOptions",field:[{name:"features",number:34,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"deprecated",number:33,type:8,label:1,defaultValue:"false"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"MethodOptions",field:[{name:"deprecated",number:33,type:8,label:1,defaultValue:"false"},{name:"idempotency_level",number:34,type:14,label:1,typeName:".google.protobuf.MethodOptions.IdempotencyLevel",defaultValue:"IDEMPOTENCY_UNKNOWN"},{name:"features",number:35,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],enumType:[{name:"IdempotencyLevel",value:[{name:"IDEMPOTENCY_UNKNOWN",number:0},{name:"NO_SIDE_EFFECTS",number:1},{name:"IDEMPOTENT",number:2}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"UninterpretedOption",field:[{name:"name",number:2,type:11,label:3,typeName:".google.protobuf.UninterpretedOption.NamePart"},{name:"identifier_value",number:3,type:9,label:1},{name:"positive_int_value",number:4,type:4,label:1},{name:"negative_int_value",number:5,type:3,label:1},{name:"double_value",number:6,type:1,label:1},{name:"string_value",number:7,type:12,label:1},{name:"aggregate_value",number:8,type:9,label:1}],nestedType:[{name:"NamePart",field:[{name:"name_part",number:1,type:9,label:2},{name:"is_extension",number:2,type:8,label:2}]}]},{name:"FeatureSet",field:[{name:"field_presence",number:1,type:14,label:1,typeName:".google.protobuf.FeatureSet.FieldPresence",options:{retention:1,targets:[4,1],editionDefaults:[{value:"EXPLICIT",edition:900},{value:"IMPLICIT",edition:999},{value:"EXPLICIT",edition:1e3}]}},{name:"enum_type",number:2,type:14,label:1,typeName:".google.protobuf.FeatureSet.EnumType",options:{retention:1,targets:[6,1],editionDefaults:[{value:"CLOSED",edition:900},{value:"OPEN",edition:999}]}},{name:"repeated_field_encoding",number:3,type:14,label:1,typeName:".google.protobuf.FeatureSet.RepeatedFieldEncoding",options:{retention:1,targets:[4,1],editionDefaults:[{value:"EXPANDED",edition:900},{value:"PACKED",edition:999}]}},{name:"utf8_validation",number:4,type:14,label:1,typeName:".google.protobuf.FeatureSet.Utf8Validation",options:{retention:1,targets:[4,1],editionDefaults:[{value:"NONE",edition:900},{value:"VERIFY",edition:999}]}},{name:"message_encoding",number:5,type:14,label:1,typeName:".google.protobuf.FeatureSet.MessageEncoding",options:{retention:1,targets:[4,1],editionDefaults:[{value:"LENGTH_PREFIXED",edition:900}]}},{name:"json_format",number:6,type:14,label:1,typeName:".google.protobuf.FeatureSet.JsonFormat",options:{retention:1,targets:[3,6,1],editionDefaults:[{value:"LEGACY_BEST_EFFORT",edition:900},{value:"ALLOW",edition:999}]}},{name:"enforce_naming_style",number:7,type:14,label:1,typeName:".google.protobuf.FeatureSet.EnforceNamingStyle",options:{retention:2,targets:[1,2,3,4,5,6,7,8,9],editionDefaults:[{value:"STYLE_LEGACY",edition:900},{value:"STYLE2024",edition:1001}]}}],enumType:[{name:"FieldPresence",value:[{name:"FIELD_PRESENCE_UNKNOWN",number:0},{name:"EXPLICIT",number:1},{name:"IMPLICIT",number:2},{name:"LEGACY_REQUIRED",number:3}]},{name:"EnumType",value:[{name:"ENUM_TYPE_UNKNOWN",number:0},{name:"OPEN",number:1},{name:"CLOSED",number:2}]},{name:"RepeatedFieldEncoding",value:[{name:"REPEATED_FIELD_ENCODING_UNKNOWN",number:0},{name:"PACKED",number:1},{name:"EXPANDED",number:2}]},{name:"Utf8Validation",value:[{name:"UTF8_VALIDATION_UNKNOWN",number:0},{name:"VERIFY",number:2},{name:"NONE",number:3}]},{name:"MessageEncoding",value:[{name:"MESSAGE_ENCODING_UNKNOWN",number:0},{name:"LENGTH_PREFIXED",number:1},{name:"DELIMITED",number:2}]},{name:"JsonFormat",value:[{name:"JSON_FORMAT_UNKNOWN",number:0},{name:"ALLOW",number:1},{name:"LEGACY_BEST_EFFORT",number:2}]},{name:"EnforceNamingStyle",value:[{name:"ENFORCE_NAMING_STYLE_UNKNOWN",number:0},{name:"STYLE2024",number:1},{name:"STYLE_LEGACY",number:2}]}],extensionRange:[{start:1e3,end:9995},{start:9995,end:1e4},{start:1e4,end:10001}]},{name:"FeatureSetDefaults",field:[{name:"defaults",number:1,type:11,label:3,typeName:".google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault"},{name:"minimum_edition",number:4,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"maximum_edition",number:5,type:14,label:1,typeName:".google.protobuf.Edition"}],nestedType:[{name:"FeatureSetEditionDefault",field:[{name:"edition",number:3,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"overridable_features",number:4,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"fixed_features",number:5,type:11,label:1,typeName:".google.protobuf.FeatureSet"}]}]},{name:"SourceCodeInfo",field:[{name:"location",number:1,type:11,label:3,typeName:".google.protobuf.SourceCodeInfo.Location"}],nestedType:[{name:"Location",field:[{name:"path",number:1,type:5,label:3,options:{packed:!0}},{name:"span",number:2,type:5,label:3,options:{packed:!0}},{name:"leading_comments",number:3,type:9,label:1},{name:"trailing_comments",number:4,type:9,label:1},{name:"leading_detached_comments",number:6,type:9,label:3}]}],extensionRange:[{start:536e6,end:536000001}]},{name:"GeneratedCodeInfo",field:[{name:"annotation",number:1,type:11,label:3,typeName:".google.protobuf.GeneratedCodeInfo.Annotation"}],nestedType:[{name:"Annotation",field:[{name:"path",number:1,type:5,label:3,options:{packed:!0}},{name:"source_file",number:2,type:9,label:1},{name:"begin",number:3,type:5,label:1},{name:"end",number:4,type:5,label:1},{name:"semantic",number:5,type:14,label:1,typeName:".google.protobuf.GeneratedCodeInfo.Annotation.Semantic"}],enumType:[{name:"Semantic",value:[{name:"NONE",number:0},{name:"SET",number:1},{name:"ALIAS",number:2}]}]}]}],enumType:[{name:"Edition",value:[{name:"EDITION_UNKNOWN",number:0},{name:"EDITION_LEGACY",number:900},{name:"EDITION_PROTO2",number:998},{name:"EDITION_PROTO3",number:999},{name:"EDITION_2023",number:1e3},{name:"EDITION_2024",number:1001},{name:"EDITION_1_TEST_ONLY",number:1},{name:"EDITION_2_TEST_ONLY",number:2},{name:"EDITION_99997_TEST_ONLY",number:99997},{name:"EDITION_99998_TEST_ONLY",number:99998},{name:"EDITION_99999_TEST_ONLY",number:99999},{name:"EDITION_MAX",number:2147483647}]}]}),1);var er,tr,nr,sr,rr,ar,or,ir,lr,ur,cr,dr,mr,hr,pr,gr,fr,yr;(function(t){t[t.DECLARATION=0]="DECLARATION",t[t.UNVERIFIED=1]="UNVERIFIED"})(er||(er={})),function(t){t[t.DOUBLE=1]="DOUBLE",t[t.FLOAT=2]="FLOAT",t[t.INT64=3]="INT64",t[t.UINT64=4]="UINT64",t[t.INT32=5]="INT32",t[t.FIXED64=6]="FIXED64",t[t.FIXED32=7]="FIXED32",t[t.BOOL=8]="BOOL",t[t.STRING=9]="STRING",t[t.GROUP=10]="GROUP",t[t.MESSAGE=11]="MESSAGE",t[t.BYTES=12]="BYTES",t[t.UINT32=13]="UINT32",t[t.ENUM=14]="ENUM",t[t.SFIXED32=15]="SFIXED32",t[t.SFIXED64=16]="SFIXED64",t[t.SINT32=17]="SINT32",t[t.SINT64=18]="SINT64"}(tr||(tr={})),function(t){t[t.OPTIONAL=1]="OPTIONAL",t[t.REPEATED=3]="REPEATED",t[t.REQUIRED=2]="REQUIRED"}(nr||(nr={})),function(t){t[t.SPEED=1]="SPEED",t[t.CODE_SIZE=2]="CODE_SIZE",t[t.LITE_RUNTIME=3]="LITE_RUNTIME"}(sr||(sr={})),function(t){t[t.STRING=0]="STRING",t[t.CORD=1]="CORD",t[t.STRING_PIECE=2]="STRING_PIECE"}(rr||(rr={})),function(t){t[t.JS_NORMAL=0]="JS_NORMAL",t[t.JS_STRING=1]="JS_STRING",t[t.JS_NUMBER=2]="JS_NUMBER"}(ar||(ar={})),function(t){t[t.RETENTION_UNKNOWN=0]="RETENTION_UNKNOWN",t[t.RETENTION_RUNTIME=1]="RETENTION_RUNTIME",t[t.RETENTION_SOURCE=2]="RETENTION_SOURCE"}(or||(or={})),function(t){t[t.TARGET_TYPE_UNKNOWN=0]="TARGET_TYPE_UNKNOWN",t[t.TARGET_TYPE_FILE=1]="TARGET_TYPE_FILE",t[t.TARGET_TYPE_EXTENSION_RANGE=2]="TARGET_TYPE_EXTENSION_RANGE",t[t.TARGET_TYPE_MESSAGE=3]="TARGET_TYPE_MESSAGE",t[t.TARGET_TYPE_FIELD=4]="TARGET_TYPE_FIELD",t[t.TARGET_TYPE_ONEOF=5]="TARGET_TYPE_ONEOF",t[t.TARGET_TYPE_ENUM=6]="TARGET_TYPE_ENUM",t[t.TARGET_TYPE_ENUM_ENTRY=7]="TARGET_TYPE_ENUM_ENTRY",t[t.TARGET_TYPE_SERVICE=8]="TARGET_TYPE_SERVICE",t[t.TARGET_TYPE_METHOD=9]="TARGET_TYPE_METHOD"}(ir||(ir={})),function(t){t[t.IDEMPOTENCY_UNKNOWN=0]="IDEMPOTENCY_UNKNOWN",t[t.NO_SIDE_EFFECTS=1]="NO_SIDE_EFFECTS",t[t.IDEMPOTENT=2]="IDEMPOTENT"}(lr||(lr={})),function(t){t[t.FIELD_PRESENCE_UNKNOWN=0]="FIELD_PRESENCE_UNKNOWN",t[t.EXPLICIT=1]="EXPLICIT",t[t.IMPLICIT=2]="IMPLICIT",t[t.LEGACY_REQUIRED=3]="LEGACY_REQUIRED"}(ur||(ur={})),function(t){t[t.ENUM_TYPE_UNKNOWN=0]="ENUM_TYPE_UNKNOWN",t[t.OPEN=1]="OPEN",t[t.CLOSED=2]="CLOSED"}(cr||(cr={})),function(t){t[t.REPEATED_FIELD_ENCODING_UNKNOWN=0]="REPEATED_FIELD_ENCODING_UNKNOWN",t[t.PACKED=1]="PACKED",t[t.EXPANDED=2]="EXPANDED"}(dr||(dr={})),function(t){t[t.UTF8_VALIDATION_UNKNOWN=0]="UTF8_VALIDATION_UNKNOWN",t[t.VERIFY=2]="VERIFY",t[t.NONE=3]="NONE"}(mr||(mr={})),function(t){t[t.MESSAGE_ENCODING_UNKNOWN=0]="MESSAGE_ENCODING_UNKNOWN",t[t.LENGTH_PREFIXED=1]="LENGTH_PREFIXED",t[t.DELIMITED=2]="DELIMITED"}(hr||(hr={})),function(t){t[t.JSON_FORMAT_UNKNOWN=0]="JSON_FORMAT_UNKNOWN",t[t.ALLOW=1]="ALLOW",t[t.LEGACY_BEST_EFFORT=2]="LEGACY_BEST_EFFORT"}(pr||(pr={})),function(t){t[t.ENFORCE_NAMING_STYLE_UNKNOWN=0]="ENFORCE_NAMING_STYLE_UNKNOWN",t[t.STYLE2024=1]="STYLE2024",t[t.STYLE_LEGACY=2]="STYLE_LEGACY"}(gr||(gr={})),function(t){t[t.NONE=0]="NONE",t[t.SET=1]="SET",t[t.ALIAS=2]="ALIAS"}(fr||(fr={})),function(t){t[t.EDITION_UNKNOWN=0]="EDITION_UNKNOWN",t[t.EDITION_LEGACY=900]="EDITION_LEGACY",t[t.EDITION_PROTO2=998]="EDITION_PROTO2",t[t.EDITION_PROTO3=999]="EDITION_PROTO3",t[t.EDITION_2023=1e3]="EDITION_2023",t[t.EDITION_2024=1001]="EDITION_2024",t[t.EDITION_1_TEST_ONLY=1]="EDITION_1_TEST_ONLY",t[t.EDITION_2_TEST_ONLY=2]="EDITION_2_TEST_ONLY",t[t.EDITION_99997_TEST_ONLY=99997]="EDITION_99997_TEST_ONLY",t[t.EDITION_99998_TEST_ONLY=99998]="EDITION_99998_TEST_ONLY",t[t.EDITION_99999_TEST_ONLY=99999]="EDITION_99999_TEST_ONLY",t[t.EDITION_MAX=2147483647]="EDITION_MAX"}(yr||(yr={}));const Ri={readUnknownFields:!0};function Ss(t,e,n){const s=ue(t,void 0,!1);return Pa(s,new fs(e),Ri,!1,e.byteLength),s.message}function Pa(t,e,n,s,r){var a;const o=s?e.len:e.pos+r;let i,l;const u=(a=t.getUnknown())!==null&&a!==void 0?a:[];for(;e.pos<o&&([i,l]=e.tag(),!s||l!=U.EndGroup);){const d=t.findNumber(i);if(d)La(t,e,d,l,n);else{const m=e.skip(l,i);n.readUnknownFields&&u.push({no:i,wireType:l,data:m})}}if(s&&(l!=U.EndGroup||i!==r))throw new Error("invalid end group tag");u.length>0&&t.setUnknown(u)}function La(t,e,n,s,r){switch(n.fieldKind){case"scalar":t.set(n,Ze(e,n.scalar));break;case"enum":t.set(n,Ze(e,g.INT32));break;case"message":t.set(n,kn(e,r,n,t.get(n)));break;case"list":(function(a,o,i,l){var u;const d=i.field();if(d.listKind==="message")return void i.add(kn(a,l,d));const m=(u=d.scalar)!==null&&u!==void 0?u:g.INT32;if(!(o==U.LengthDelimited&&m!=g.STRING&&m!=g.BYTES))return void i.add(Ze(a,m));const h=a.uint32()+a.pos;for(;a.pos<h;)i.add(Ze(a,m))})(e,s,t.get(n),r);break;case"map":(function(a,o,i){const l=o.field();let u,d;const m=a.pos+a.uint32();for(;a.pos<m;){const[p]=a.tag();switch(p){case 1:u=Ze(a,l.mapKey);break;case 2:switch(l.mapKind){case"scalar":d=Ze(a,l.scalar);break;case"enum":d=a.int32();break;case"message":d=kn(a,i,l)}}}if(u===void 0&&(u=We(l.mapKey,!1)),d===void 0)switch(l.mapKind){case"scalar":d=We(l.scalar,!1);break;case"enum":d=l.enum.values[0].number;break;case"message":d=ue(l.message,void 0,!1)}o.set(u,d)})(e,t.get(n),r)}}function kn(t,e,n,s){const r=n.delimitedEncoding,a=s??ue(n.message,void 0,!1);return Pa(a,t,e,r,r?n.number:t.uint32()),a}function Ze(t,e){switch(e){case g.STRING:return t.string();case g.BOOL:return t.bool();case g.DOUBLE:return t.double();case g.FLOAT:return t.float();case g.INT32:return t.int32();case g.INT64:return t.int64();case g.UINT64:return t.uint64();case g.FIXED64:return t.fixed64();case g.BYTES:return t.bytes();case g.FIXED32:return t.fixed32();case g.SFIXED32:return t.sfixed32();case g.SFIXED64:return t.sfixed64();case g.SINT64:return t.sint64();case g.UINT32:return t.uint32();case g.SINT32:return t.sint32()}}function Is(t,e){const n=Ss(Ni,ka(t));return n.messageType.forEach(Ts),n.dependency=[],Aa(n,s=>{}).getFile(n.name)}const Ci=kt(Is("Chlnb29nbGUvcHJvdG9idWYvYW55LnByb3RvEg9nb29nbGUucHJvdG9idWYiJgoDQW55EhAKCHR5cGVfdXJsGAEgASgJEg0KBXZhbHVlGAIgASgMQnYKE2NvbS5nb29nbGUucHJvdG9idWZCCEFueVByb3RvUAFaLGdvb2dsZS5nb2xhbmcub3JnL3Byb3RvYnVmL3R5cGVzL2tub3duL2FueXBiogIDR1BCqgIeR29vZ2xlLlByb3RvYnVmLldlbGxLbm93blR5cGVzYgZwcm90bzM"),0),ki=3,_r={writeUnknownFields:!0};function xi(t,e,n){return en(new ba,function(s){return s?Object.assign(Object.assign({},_r),s):_r}(n),ue(t,e)).finish()}function en(t,e,n){var s;for(const r of n.sortedFields)if(n.isSet(r))$a(t,e,n,r);else if(r.presence==ki)throw new Error(`cannot encode ${r} to binary: required field not set`);if(e.writeUnknownFields)for(const{no:r,wireType:a,data:o}of(s=n.getUnknown())!==null&&s!==void 0?s:[])t.tag(r,a).raw(o);return t}function $a(t,e,n,s){var r;switch(s.fieldKind){case"scalar":case"enum":tn(t,n.desc.typeName,s.name,(r=s.scalar)!==null&&r!==void 0?r:g.INT32,s.number,n.get(s));break;case"list":(function(a,o,i,l){var u;if(i.listKind=="message"){for(const m of l)br(a,o,i,m);return}const d=(u=i.scalar)!==null&&u!==void 0?u:g.INT32;if(i.packed){if(!l.size)return;a.tag(i.number,U.LengthDelimited).fork();for(const m of l)qa(a,i.parent.typeName,i.name,d,m);return void a.join()}for(const m of l)tn(a,i.parent.typeName,i.name,d,i.number,m)})(t,e,s,n.get(s));break;case"message":br(t,e,s,n.get(s));break;case"map":for(const[a,o]of n.get(s))Ai(t,e,s,a,o)}}function tn(t,e,n,s,r,a){qa(t.tag(r,function(o){switch(o){case g.BYTES:case g.STRING:return U.LengthDelimited;case g.DOUBLE:case g.FIXED64:case g.SFIXED64:return U.Bit64;case g.FIXED32:case g.SFIXED32:case g.FLOAT:return U.Bit32;default:return U.Varint}}(s)),e,n,s,a)}function br(t,e,n,s){n.delimitedEncoding?en(t.tag(n.number,U.StartGroup),e,s).tag(n.number,U.EndGroup):en(t.tag(n.number,U.LengthDelimited).fork(),e,s).join()}function Ai(t,e,n,s,r){var a;switch(t.tag(n.number,U.LengthDelimited).fork(),tn(t,n.parent.typeName,n.name,n.mapKey,1,s),n.mapKind){case"scalar":case"enum":tn(t,n.parent.typeName,n.name,(a=n.scalar)!==null&&a!==void 0?a:g.INT32,2,r);break;case"message":en(t.tag(2,U.LengthDelimited).fork(),e,r).join()}t.join()}function qa(t,e,n,s,r){try{switch(s){case g.STRING:t.string(r);break;case g.BOOL:t.bool(r);break;case g.DOUBLE:t.double(r);break;case g.FLOAT:t.float(r);break;case g.INT32:t.int32(r);break;case g.INT64:t.int64(r);break;case g.UINT64:t.uint64(r);break;case g.FIXED64:t.fixed64(r);break;case g.BYTES:t.bytes(r);break;case g.FIXED32:t.fixed32(r);break;case g.SFIXED32:t.sfixed32(r);break;case g.SFIXED64:t.sfixed64(r);break;case g.SINT64:t.sint64(r);break;case g.UINT32:t.uint32(r);break;case g.SINT32:t.sint32(r)}}catch(a){throw a instanceof Error?new Error(`cannot encode field ${e}.${n} to binary: ${a.message}`):a}}function Mi(t,e){if(t.typeUrl==="")return;const n=e.kind=="message"?e:e.getMessage(vr(t.typeUrl));return n&&function(s,r){return s.typeUrl!==""&&(typeof r=="string"?r:r.typeName)===vr(s.typeUrl)}(t,n)?Ss(n,t.value):void 0}function vr(t){const e=t.lastIndexOf("/"),n=e>=0?t.substring(e+1):t;if(!n.length)throw new Error(`invalid type url: ${t}`);return n}const ws=Is("Chxnb29nbGUvcHJvdG9idWYvc3RydWN0LnByb3RvEg9nb29nbGUucHJvdG9idWYihAEKBlN0cnVjdBIzCgZmaWVsZHMYASADKAsyIy5nb29nbGUucHJvdG9idWYuU3RydWN0LkZpZWxkc0VudHJ5GkUKC0ZpZWxkc0VudHJ5EgsKA2tleRgBIAEoCRIlCgV2YWx1ZRgCIAEoCzIWLmdvb2dsZS5wcm90b2J1Zi5WYWx1ZToCOAEi6gEKBVZhbHVlEjAKCm51bGxfdmFsdWUYASABKA4yGi5nb29nbGUucHJvdG9idWYuTnVsbFZhbHVlSAASFgoMbnVtYmVyX3ZhbHVlGAIgASgBSAASFgoMc3RyaW5nX3ZhbHVlGAMgASgJSAASFAoKYm9vbF92YWx1ZRgEIAEoCEgAEi8KDHN0cnVjdF92YWx1ZRgFIAEoCzIXLmdvb2dsZS5wcm90b2J1Zi5TdHJ1Y3RIABIwCgpsaXN0X3ZhbHVlGAYgASgLMhouZ29vZ2xlLnByb3RvYnVmLkxpc3RWYWx1ZUgAQgYKBGtpbmQiMwoJTGlzdFZhbHVlEiYKBnZhbHVlcxgBIAMoCzIWLmdvb2dsZS5wcm90b2J1Zi5WYWx1ZSobCglOdWxsVmFsdWUSDgoKTlVMTF9WQUxVRRAAQn8KE2NvbS5nb29nbGUucHJvdG9idWZCC1N0cnVjdFByb3RvUAFaL2dvb2dsZS5nb2xhbmcub3JnL3Byb3RvYnVmL3R5cGVzL2tub3duL3N0cnVjdHBi+AEBogIDR1BCqgIeR29vZ2xlLlByb3RvYnVmLldlbGxLbm93blR5cGVzYgZwcm90bzM"),Oi=kt(ws,0),Ha=kt(ws,1),Di=kt(ws,2);var es;function Fi(t,e){Ga(e,t);const n=function(o,i){if(o===void 0)return[];if(i.fieldKind==="enum"||i.fieldKind==="scalar"){for(let l=o.length-1;l>=0;--l)if(o[l].no==i.number)return[o[l]];return[]}return o.filter(l=>l.no===i.number)}(t.$unknown,e),[s,r,a]=_n(e);for(const o of n)La(s,new fs(o.data),r,o.wireType,{readUnknownFields:!0});return a()}function Ui(t,e,n){var s;Ga(e,t);const r=((s=t.$unknown)!==null&&s!==void 0?s:[]).filter(u=>u.no!==e.number),[a,o]=_n(e,n),i=new ba;$a(i,{writeUnknownFields:!0},a,o);const l=new fs(i.finish());for(;l.pos<l.len;){const[u,d]=l.tag(),m=l.skip(d,u);r.push({no:u,wireType:d,data:m})}t.$unknown=r}function _n(t,e){const n=t.typeName,s=Object.assign(Object.assign({},t),{kind:"field",parent:t.extendee,localName:n}),r=Object.assign(Object.assign({},t.extendee),{fields:[s],members:[s],oneofs:[]}),a=ye(r,e!==void 0?{[n]:e}:void 0);return[ue(r,a),s,()=>{const o=a[n];if(o===void 0){const i=t.message;return Ct(i)?We(i.fields[0].scalar,i.fields[0].longAsString):ye(i)}return o}]}function Ga(t,e){if(t.extendee.typeName!=e.$typeName)throw new Error(`extension ${t.typeName} can only be applied to message ${t.extendee.typeName}`)}(function(t){t[t.NULL_VALUE=0]="NULL_VALUE"})(es||(es={}));const Pi=3,Li=2,Er={alwaysEmitImplicit:!1,enumAsInteger:!1,useProtoFieldName:!1};function $i(t,e,n){return vt(ue(t,e),function(s){return s?Object.assign(Object.assign({},Er),s):Er}(n))}function vt(t,e){var n;const s=function(a,o){if(a.desc.typeName.startsWith("google.protobuf.")){switch(a.desc.typeName){case"google.protobuf.Any":return function(l,u){if(l.typeUrl==="")return{};const{registry:d}=u;let m,p;if(d&&(m=Mi(l,d),m&&(p=d.getMessage(m.$typeName))),!p||!m)throw new Error(`cannot encode message ${l.$typeName} to JSON: "${l.typeUrl}" is not in the type registry`);let h=vt(ue(p,m),u);return(p.typeName.startsWith("google.protobuf.")||h===null||Array.isArray(h)||typeof h!="object")&&(h={value:h}),h["@type"]=l.typeUrl,h}(a.message,o);case"google.protobuf.Timestamp":return function(l){const u=1e3*Number(l.seconds);if(u<Date.parse("0001-01-01T00:00:00Z")||u>Date.parse("9999-12-31T23:59:59Z"))throw new Error(`cannot encode message ${l.$typeName} to JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive`);if(l.nanos<0)throw new Error(`cannot encode message ${l.$typeName} to JSON: nanos must not be negative`);let d="Z";if(l.nanos>0){const m=(l.nanos+1e9).toString().substring(1);d=m.substring(3)==="000000"?"."+m.substring(0,3)+"Z":m.substring(6)==="000"?"."+m.substring(0,6)+"Z":"."+m+"Z"}return new Date(u).toISOString().replace(".000Z",d)}(a.message);case"google.protobuf.Duration":return function(l){if(Number(l.seconds)>315576e6||Number(l.seconds)<-315576e6)throw new Error(`cannot encode message ${l.$typeName} to JSON: value out of range`);let u=l.seconds.toString();if(l.nanos!==0){let d=Math.abs(l.nanos).toString();d="0".repeat(9-d.length)+d,d.substring(3)==="000000"?d=d.substring(0,3):d.substring(6)==="000"&&(d=d.substring(0,6)),u+="."+d,l.nanos<0&&Number(l.seconds)==0&&(u="-"+u)}return u+"s"}(a.message);case"google.protobuf.FieldMask":return(i=a.message).paths.map(l=>{if(l.match(/_[0-9]?_/g)||l.match(/[A-Z]/g))throw new Error(`cannot encode message ${i.$typeName} to JSON: lowerCamelCase of path name "`+l+'" is irreversible');return It(l)}).join(",");case"google.protobuf.Struct":return Ba(a.message);case"google.protobuf.Value":return Ns(a.message);case"google.protobuf.ListValue":return Va(a.message);default:if(Ct(a.desc)){const l=a.desc.fields[0];return Yt(l,a.get(l))}return}var i}}(t,e);if(s!==void 0)return s;const r={};for(const a of t.sortedFields){if(!t.isSet(a)){if(a.presence==Pi)throw new Error(`cannot encode ${a} to JSON: required field not set`);if(!e.alwaysEmitImplicit||a.presence!==Li)continue}const o=Tr(a,t.get(a),e);o!==void 0&&(r[qi(a,e)]=o)}if(e.registry){const a=new Set;for(const{no:o}of(n=t.getUnknown())!==null&&n!==void 0?n:[])if(!a.has(o)){a.add(o);const i=e.registry.getExtensionFor(t.desc,o);if(!i)continue;const l=Fi(t.message,i),[u,d]=_n(i,l),m=Tr(d,u.get(d),e);m!==void 0&&(r[i.jsonName]=m)}}return r}function Tr(t,e,n){switch(t.fieldKind){case"scalar":return Yt(t,e);case"message":return vt(e,n);case"enum":return xn(t.enum,e,n.enumAsInteger);case"list":return function(s,r){const a=s.field(),o=[];switch(a.listKind){case"scalar":for(const i of s)o.push(Yt(a,i));break;case"enum":for(const i of s)o.push(xn(a.enum,i,r.enumAsInteger));break;case"message":for(const i of s)o.push(vt(i,r))}return r.alwaysEmitImplicit||o.length>0?o:void 0}(e,n);case"map":return function(s,r){const a=s.field(),o={};switch(a.mapKind){case"scalar":for(const[i,l]of s)o[i]=Yt(a,l);break;case"message":for(const[i,l]of s)o[i]=vt(l,r);break;case"enum":for(const[i,l]of s)o[i]=xn(a.enum,l,r.enumAsInteger)}return r.alwaysEmitImplicit||s.size>0?o:void 0}(e,n)}}function xn(t,e,n){var s;if(typeof e!="number")throw new Error(`cannot encode ${t} to JSON: expected number, got ${$(e)}`);if(t.typeName=="google.protobuf.NullValue")return null;if(n)return e;const r=t.value[e];return(s=r==null?void 0:r.name)!==null&&s!==void 0?s:e}function Yt(t,e){var n,s,r,a,o,i;switch(t.scalar){case g.INT32:case g.SFIXED32:case g.SINT32:case g.FIXED32:case g.UINT32:if(typeof e!="number")throw new Error(`cannot encode ${t} to JSON: ${(n=He(t,e))===null||n===void 0?void 0:n.message}`);return e;case g.FLOAT:case g.DOUBLE:if(typeof e!="number")throw new Error(`cannot encode ${t} to JSON: ${(s=He(t,e))===null||s===void 0?void 0:s.message}`);return Number.isNaN(e)?"NaN":e===Number.POSITIVE_INFINITY?"Infinity":e===Number.NEGATIVE_INFINITY?"-Infinity":e;case g.STRING:if(typeof e!="string")throw new Error(`cannot encode ${t} to JSON: ${(r=He(t,e))===null||r===void 0?void 0:r.message}`);return e;case g.BOOL:if(typeof e!="boolean")throw new Error(`cannot encode ${t} to JSON: ${(a=He(t,e))===null||a===void 0?void 0:a.message}`);return e;case g.UINT64:case g.FIXED64:case g.INT64:case g.SFIXED64:case g.SINT64:if(typeof e!="bigint"&&typeof e!="string")throw new Error(`cannot encode ${t} to JSON: ${(o=He(t,e))===null||o===void 0?void 0:o.message}`);return e.toString();case g.BYTES:if(e instanceof Uint8Array)return function(l,u="std"){const d=xa(u),m=u=="std";let p,h="",f=0,_=0;for(let y=0;y<l.length;y++)switch(p=l[y],f){case 0:h+=d[p>>2],_=(3&p)<<4,f=1;break;case 1:h+=d[_|p>>4],_=(15&p)<<2,f=2;break;case 2:h+=d[_|p>>6],h+=d[63&p],f=0}return f&&(h+=d[_],m&&(h+="=",f==1&&(h+="="))),h}(e);throw new Error(`cannot encode ${t} to JSON: ${(i=He(t,e))===null||i===void 0?void 0:i.message}`)}}function qi(t,e){return e.useProtoFieldName?t.name:t.jsonName}function Ba(t){const e={};for(const[n,s]of Object.entries(t.fields))e[n]=Ns(s);return e}function Ns(t){switch(t.kind.case){case"nullValue":return null;case"numberValue":if(!Number.isFinite(t.kind.value))throw new Error(`${t.$typeName} cannot be NaN or Infinity`);return t.kind.value;case"boolValue":case"stringValue":return t.kind.value;case"structValue":return Ba(t.kind.value);case"listValue":return Va(t.kind.value);default:throw new Error(`${t.$typeName} must have a value`)}}function Va(t){return t.values.map(Ns)}const Sr={ignoreUnknownFields:!1};function Hi(t,e,n){const s=ue(t);try{it(s,e,function(a){return a?Object.assign(Object.assign({},Sr),a):Sr}(n))}catch(a){throw(r=a)instanceof Error&&ei.includes(r.name)&&"field"in r&&typeof r.field=="function"?new Error(`cannot decode ${a.field()} from JSON: ${a.message}`,{cause:a}):a}var r;return s.message}function it(t,e,n){var s;if(function(o,i,l){if(!o.desc.typeName.startsWith("google.protobuf."))return!1;switch(o.desc.typeName){case"google.protobuf.Any":return function(u,d,m){var p;if(d===null||Array.isArray(d)||typeof d!="object")throw new Error(`cannot decode message ${u.$typeName} from JSON: expected object but got ${$(d)}`);if(Object.keys(d).length==0)return;const h=d["@type"];if(typeof h!="string"||h=="")throw new Error(`cannot decode message ${u.$typeName} from JSON: "@type" is empty`);const f=h.includes("/")?h.substring(h.lastIndexOf("/")+1):h;if(!f.length)throw new Error(`cannot decode message ${u.$typeName} from JSON: "@type" is invalid`);const _=(p=m.registry)===null||p===void 0?void 0:p.getMessage(f);if(!_)throw new Error(`cannot decode message ${u.$typeName} from JSON: ${h} is not in the type registry`);const y=ue(_);if(f.startsWith("google.protobuf.")&&Object.prototype.hasOwnProperty.call(d,"value"))it(y,d.value,m);else{const v=Object.assign({},d);delete v["@type"],it(y,v,m)}(function(v,E,T){let k=!1;T||(T=ye(Ci),k=!0),T.value=xi(v,E),T.typeUrl=`type.googleapis.com/${E.$typeName}`})(y.desc,y.message,u)}(o.message,i,l),!0;case"google.protobuf.Timestamp":return function(u,d){if(typeof d!="string")throw new Error(`cannot decode message ${u.$typeName} from JSON: ${$(d)}`);const m=d.match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})T([0-9]{2}):([0-9]{2}):([0-9]{2})(?:\.([0-9]{1,9}))?(?:Z|([+-][0-9][0-9]:[0-9][0-9]))$/);if(!m)throw new Error(`cannot decode message ${u.$typeName} from JSON: invalid RFC 3339 string`);const p=Date.parse(m[1]+"-"+m[2]+"-"+m[3]+"T"+m[4]+":"+m[5]+":"+m[6]+(m[8]?m[8]:"Z"));if(Number.isNaN(p))throw new Error(`cannot decode message ${u.$typeName} from JSON: invalid RFC 3339 string`);if(p<Date.parse("0001-01-01T00:00:00Z")||p>Date.parse("9999-12-31T23:59:59Z"))throw new Error(`cannot decode message ${u.$typeName} from JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive`);u.seconds=D.parse(p/1e3),u.nanos=0,m[7]&&(u.nanos=parseInt("1"+m[7]+"0".repeat(9-m[7].length))-1e9)}(o.message,i),!0;case"google.protobuf.Duration":return function(u,d){if(typeof d!="string")throw new Error(`cannot decode message ${u.$typeName} from JSON: ${$(d)}`);const m=d.match(/^(-?[0-9]+)(?:\.([0-9]+))?s/);if(m===null)throw new Error(`cannot decode message ${u.$typeName} from JSON: ${$(d)}`);const p=Number(m[1]);if(p>315576e6||p<-315576e6)throw new Error(`cannot decode message ${u.$typeName} from JSON: ${$(d)}`);if(u.seconds=D.parse(p),typeof m[2]!="string")return;const h=m[2]+"0".repeat(9-m[2].length);u.nanos=parseInt(h),(p<0||Object.is(p,-0))&&(u.nanos=-u.nanos)}(o.message,i),!0;case"google.protobuf.FieldMask":return function(u,d){if(typeof d!="string")throw new Error(`cannot decode message ${u.$typeName} from JSON: ${$(d)}`);if(d==="")return;function m(p){if(p.includes("_"))throw new Error(`cannot decode message ${u.$typeName} from JSON: path names must be lowerCamelCase`);const h=p.replace(/[A-Z]/g,f=>"_"+f.toLowerCase());return h[0]==="_"?h.substring(1):h}u.paths=d.split(",").map(m)}(o.message,i),!0;case"google.protobuf.Struct":return ja(o.message,i),!0;case"google.protobuf.Value":return Rs(o.message,i),!0;case"google.protobuf.ListValue":return Wa(o.message,i),!0;default:if(Ct(o.desc)){const u=o.desc.fields[0];return i===null?o.clear(u):o.set(u,Wt(u,i,!0)),!0}return!1}}(t,e,n))return;if(e==null||Array.isArray(e)||typeof e!="object")throw new Error(`cannot decode ${t.desc} from JSON: ${$(e)}`);const r=new Map,a=new Map;for(const o of t.desc.fields)a.set(o.name,o).set(o.jsonName,o);for(const[o,i]of Object.entries(e)){const l=a.get(o);if(l){if(l.oneof){if(i===null&&l.fieldKind=="scalar")continue;const u=r.get(l.oneof);if(u!==void 0)throw new ee(l.oneof,`oneof set multiple times by ${u.name} and ${l.name}`);r.set(l.oneof,l)}Ir(t,l,i,n)}else{let u;if(o.startsWith("[")&&o.endsWith("]")&&(u=(s=n.registry)===null||s===void 0?void 0:s.getExtension(o.substring(1,o.length-1)))&&u.extendee.typeName===t.desc.typeName){const[d,m,p]=_n(u);Ir(d,m,i,n),Ui(t.message,u,p())}if(!u&&!n.ignoreUnknownFields)throw new Error(`cannot decode ${t.desc} from JSON: key "${o}" is unknown`)}}}function Ir(t,e,n,s){switch(e.fieldKind){case"scalar":(function(r,a,o){const i=Wt(a,o,!1);i===nn?r.clear(a):r.set(a,i)})(t,e,n);break;case"enum":(function(r,a,o,i){const l=An(a.enum,o,i.ignoreUnknownFields,!1);l===nn?r.clear(a):l!==jt&&r.set(a,l)})(t,e,n,s);break;case"message":(function(r,a,o,i){if(o===null&&a.message.typeName!="google.protobuf.Value")return void r.clear(a);const l=r.isSet(a)?r.get(a):ue(a.message);it(l,o,i),r.set(a,l)})(t,e,n,s);break;case"list":(function(r,a,o){if(a===null)return;const i=r.field();if(!Array.isArray(a))throw new ee(i,"expected Array, got "+$(a));for(const l of a){if(l===null)throw new ee(i,"list item must not be null");switch(i.listKind){case"message":const u=ue(i.message);it(u,l,o),r.add(u);break;case"enum":const d=An(i.enum,l,o.ignoreUnknownFields,!0);d!==jt&&r.add(d);break;case"scalar":r.add(Wt(i,l,!0))}}})(t.get(e),n,s);break;case"map":(function(r,a,o){if(a===null)return;const i=r.field();if(typeof a!="object"||Array.isArray(a))throw new ee(i,"expected object, got "+$(a));for(const[l,u]of Object.entries(a)){if(u===null)throw new ee(i,"map value must not be null");let d;switch(i.mapKind){case"message":const p=ue(i.message);it(p,u,o),d=p;break;case"enum":if(d=An(i.enum,u,o.ignoreUnknownFields,!0),d===jt)return;break;case"scalar":d=Wt(i,u,!0)}const m=Gi(i.mapKey,l);r.set(m,d)}})(t.get(e),n,s)}}const jt=Symbol();function An(t,e,n,s){if(e===null)return t.typeName=="google.protobuf.NullValue"?0:s?t.values[0].number:nn;switch(typeof e){case"number":if(Number.isInteger(e))return e;break;case"string":const r=t.values.find(a=>a.name===e);if(r!==void 0)return r.number;if(n)return jt}throw new Error(`cannot decode ${t} from JSON: ${$(e)}`)}const nn=Symbol();function Wt(t,e,n){if(e===null)return n?We(t.scalar,!1):nn;switch(t.scalar){case g.DOUBLE:case g.FLOAT:if(e==="NaN")return NaN;if(e==="Infinity")return Number.POSITIVE_INFINITY;if(e==="-Infinity")return Number.NEGATIVE_INFINITY;if(typeof e=="number"){if(Number.isNaN(e))throw new ee(t,"unexpected NaN number");if(!Number.isFinite(e))throw new ee(t,"unexpected infinite number");break}if(typeof e=="string"){if(e===""||e.trim().length!==e.length)break;const s=Number(e);if(!Number.isFinite(s))break;return s}break;case g.INT32:case g.FIXED32:case g.SFIXED32:case g.SINT32:case g.UINT32:return Ya(e);case g.BYTES:if(typeof e=="string"){if(e==="")return new Uint8Array(0);try{return ka(e)}catch(s){const r=s instanceof Error?s.message:String(s);throw new ee(t,r)}}}return e}function Gi(t,e){switch(t){case g.BOOL:switch(e){case"true":return!0;case"false":return!1}return e;case g.INT32:case g.FIXED32:case g.UINT32:case g.SFIXED32:case g.SINT32:return Ya(e);default:return e}}function Ya(t){if(typeof t=="string"){if(t===""||t.trim().length!==t.length)return t;const e=Number(t);return Number.isNaN(e)?t:e}return t}function ja(t,e){if(typeof e!="object"||e==null||Array.isArray(e))throw new Error(`cannot decode message ${t.$typeName} from JSON ${$(e)}`);for(const[n,s]of Object.entries(e)){const r=ye(Ha);Rs(r,s),t.fields[n]=r}}function Rs(t,e){switch(typeof e){case"number":t.kind={case:"numberValue",value:e};break;case"string":t.kind={case:"stringValue",value:e};break;case"boolean":t.kind={case:"boolValue",value:e};break;case"object":if(e===null)t.kind={case:"nullValue",value:es.NULL_VALUE};else if(Array.isArray(e)){const n=ye(Di);Wa(n,e),t.kind={case:"listValue",value:n}}else{const n=ye(Oi);ja(n,e),t.kind={case:"structValue",value:n}}break;default:throw new Error(`cannot decode message ${t.$typeName} from JSON ${$(e)}`)}return t}function Wa(t,e){if(!Array.isArray(e))throw new Error(`cannot decode message ${t.$typeName} from JSON ${$(e)}`);for(const n of e){const s=ye(Ha);Rs(s,n),t.values.push(s)}}class Ka{constructor(e){c(this,"target");c(this,"pendingRequests",new Map);c(this,"cleanup");c(this,"serviceRegistries",new Set);this.target=e,this.cleanup=this.target.onReceiveMessage(this.handleMessage.bind(this))}addServiceRegistry(e){this.serviceRegistries.add(e)}removeServiceRegistry(e){this.serviceRegistries.delete(e)}handleMessage(e){if(!e||typeof e!="object"||!this.isGrpcMessageLike(e))return;const n=e;n.type==="com.augmentcode.client.rpc.request"?this.handleRequest(n):n.type==="com.augmentcode.client.rpc.response"&&this.handleResponse(n)}isGrpcMessageLike(e){return"type"in e&&e.type==="com.augmentcode.client.rpc.request"||e.type==="com.augmentcode.client.rpc.response"}async handleRequest(e){for(const n of this.serviceRegistries)if(n.canHandle(e))try{return void await n.handleRequest(e,s=>{this.target.sendMessage(s)})}catch(s){Array.from(this.serviceRegistries).indexOf(n)===this.serviceRegistries.size-1&&this.target.sendMessage({type:"com.augmentcode.client.rpc.response",id:e.id,methodLocalName:e.methodLocalName,serviceTypeName:e.serviceTypeName,data:"",error:s instanceof Error?s.message:String(s)})}this.target.sendMessage({type:"com.augmentcode.client.rpc.response",id:e.id,methodLocalName:e.methodLocalName,serviceTypeName:e.serviceTypeName,data:"",error:`No handlers registered for service: ${e.serviceTypeName}`})}handleResponse(e){const n=this.pendingRequests.get(e.id);if(n)if(this.pendingRequests.delete(e.id),clearTimeout(n.timeout),e.error)n.reject(new Error(`gRPC server error for ${e.serviceTypeName}.${e.methodLocalName} (ID: ${e.id}): ${e.error}`));else try{if(!e.data&&e.data!==null&&e.data!=="")throw new Error(`gRPC response missing data field for ${e.serviceTypeName}.${e.methodLocalName} (ID: ${e.id})`);n.resolve(e)}catch(s){const r=s instanceof Error?s.message:String(s);n.reject(new Error(`Failed to process gRPC response for ${e.serviceTypeName}.${e.methodLocalName} (ID: ${e.id}): ${r}`))}}sendRequest(e,n){return new Promise((s,r)=>{let a;n&&(a=setTimeout(()=>{this.pendingRequests.delete(e.id),r(new Error(`gRPC request timed out after ${n}ms: ${e.serviceTypeName}.${e.methodLocalName} (ID: ${e.id}). This may indicate that the server is not responding or the message routing is broken.`))},n)),this.pendingRequests.set(e.id,{resolve:s,reject:r,timeout:a}),this.target.sendMessage(e)})}async unary(e,n,s,r,a,o){const i=crypto.randomUUID(),l=e.localName,u=e.parent.typeName;if(!u)throw new Error("Service name is required for unary calls");const d=a?$i(e.input,ye(e.input,a)):{};if(n!=null&&n.aborted)throw new Error(`gRPC request aborted before sending: ${u}.${l} (ID: ${i})`);let m;n&&(m=()=>{const h=this.pendingRequests.get(i);h&&(this.pendingRequests.delete(i),clearTimeout(h.timeout),h.reject(new Error(`gRPC request aborted during execution: ${u}.${l} (ID: ${i})`)))},n.addEventListener("abort",m));const p=await this.sendRequest({type:"com.augmentcode.client.rpc.request",id:i,methodLocalName:l,serviceTypeName:u,data:d,timeout:s},s);return n&&m&&n.removeEventListener("abort",m),{stream:!1,method:e,service:e.parent,header:new Headers(r),message:Hi(e.output,p.data),trailer:new Headers}}stream(e,n,s,r,a,o){throw new Error("Streaming is not supported by this transport")}dispose(){this.cleanup();for(const{timeout:e}of this.pendingRequests.values())clearTimeout(e);this.pendingRequests.clear(),this.serviceRegistries.clear()}}c(Ka,"PROTOCOL_NAME","com.augmentcode.client.rpc");var Le;function wr(t){const e=Le[t];return typeof e!="string"?t.toString():e[0].toLowerCase()+e.substring(1).replace(/[A-Z]/g,n=>"_"+n.toLowerCase())}(function(t){t[t.Canceled=1]="Canceled",t[t.Unknown=2]="Unknown",t[t.InvalidArgument=3]="InvalidArgument",t[t.DeadlineExceeded=4]="DeadlineExceeded",t[t.NotFound=5]="NotFound",t[t.AlreadyExists=6]="AlreadyExists",t[t.PermissionDenied=7]="PermissionDenied",t[t.ResourceExhausted=8]="ResourceExhausted",t[t.FailedPrecondition=9]="FailedPrecondition",t[t.Aborted=10]="Aborted",t[t.OutOfRange=11]="OutOfRange",t[t.Unimplemented=12]="Unimplemented",t[t.Internal=13]="Internal",t[t.Unavailable=14]="Unavailable",t[t.DataLoss=15]="DataLoss",t[t.Unauthenticated=16]="Unauthenticated"})(Le||(Le={}));class De extends Error{constructor(e,n=Le.Unknown,s,r,a){super(function(o,i){return o.length?`[${wr(i)}] ${o}`:`[${wr(i)}]`}(e,n)),this.name="ConnectError",Object.setPrototypeOf(this,new.target.prototype),this.rawMessage=e,this.code=n,this.metadata=new Headers(s??{}),this.details=r??[],this.cause=a}static from(e,n=Le.Unknown){return e instanceof De?e:e instanceof Error?e.name=="AbortError"?new De(e.message,Le.Canceled):new De(e.message,n,void 0,void 0,e):new De(String(e),n,void 0,void 0,e)}static[Symbol.hasInstance](e){return e instanceof Error&&(Object.getPrototypeOf(e)===De.prototype||e.name==="ConnectError"&&"code"in e&&typeof e.code=="number"&&"metadata"in e&&"details"in e&&Array.isArray(e.details)&&"rawMessage"in e&&typeof e.rawMessage=="string"&&"cause"in e)}findDetails(e){const n=e.kind==="message"?{getMessage:r=>r===e.typeName?e:void 0}:e,s=[];for(const r of this.details){if("desc"in r){n.getMessage(r.desc.typeName)&&s.push(ye(r.desc,r.value));continue}const a=n.getMessage(r.type);if(a)try{s.push(Ss(a,r.value))}catch{}}return s}}var Bi=function(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,n=t[Symbol.asyncIterator];return n?n.call(t):(t=typeof __values=="function"?__values(t):t[Symbol.iterator](),e={},s("next"),s("throw"),s("return"),e[Symbol.asyncIterator]=function(){return this},e);function s(r){e[r]=t[r]&&function(a){return new Promise(function(o,i){(function(l,u,d,m){Promise.resolve(m).then(function(p){l({value:p,done:d})},u)})(o,i,(a=t[r](a)).done,a.value)})}}},Nt=function(t){return this instanceof Nt?(this.v=t,this):new Nt(t)},Vi=function(t,e,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s,r=n.apply(t,e||[]),a=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),o("next"),o("throw"),o("return",function(m){return function(p){return Promise.resolve(p).then(m,u)}}),s[Symbol.asyncIterator]=function(){return this},s;function o(m,p){r[m]&&(s[m]=function(h){return new Promise(function(f,_){a.push([m,h,f,_])>1||i(m,h)})},p&&(s[m]=p(s[m])))}function i(m,p){try{(h=r[m](p)).value instanceof Nt?Promise.resolve(h.value.v).then(l,u):d(a[0][2],h)}catch(f){d(a[0][3],f)}var h}function l(m){i("next",m)}function u(m){i("throw",m)}function d(m,p){m(p),a.shift(),a.length&&i(a[0][0],a[0][1])}},Yi=function(t){var e,n;return e={},s("next"),s("throw",function(r){throw r}),s("return"),e[Symbol.iterator]=function(){return this},e;function s(r,a){e[r]=t[r]?function(o){return(n=!n)?{value:Nt(t[r](o)),done:!1}:a?a(o):o}:a}},za=function(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,n=t[Symbol.asyncIterator];return n?n.call(t):(t=typeof __values=="function"?__values(t):t[Symbol.iterator](),e={},s("next"),s("throw"),s("return"),e[Symbol.asyncIterator]=function(){return this},e);function s(r){e[r]=t[r]&&function(a){return new Promise(function(o,i){(function(l,u,d,m){Promise.resolve(m).then(function(p){l({value:p,done:d})},u)})(o,i,(a=t[r](a)).done,a.value)})}}},ut=function(t){return this instanceof ut?(this.v=t,this):new ut(t)},ji=function(t){var e,n;return e={},s("next"),s("throw",function(r){throw r}),s("return"),e[Symbol.iterator]=function(){return this},e;function s(r,a){e[r]=t[r]?function(o){return(n=!n)?{value:ut(t[r](o)),done:!1}:a?a(o):o}:a}},Wi=function(t,e,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s,r=n.apply(t,e||[]),a=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),o("next"),o("throw"),o("return",function(m){return function(p){return Promise.resolve(p).then(m,u)}}),s[Symbol.asyncIterator]=function(){return this},s;function o(m,p){r[m]&&(s[m]=function(h){return new Promise(function(f,_){a.push([m,h,f,_])>1||i(m,h)})},p&&(s[m]=p(s[m])))}function i(m,p){try{(h=r[m](p)).value instanceof ut?Promise.resolve(h.value.v).then(l,u):d(a[0][2],h)}catch(f){d(a[0][3],f)}var h}function l(m){i("next",m)}function u(m){i("throw",m)}function d(m,p){m(p),a.shift(),a.length&&i(a[0][0],a[0][1])}};function Ki(t,e){return function(n,s){const r={};for(const a of n.methods){const o=s(a);o!=null&&(r[a.localName]=o)}return r}(t,n=>{switch(n.methodKind){case"unary":return function(s,r){return async function(a,o){var i,l;const u=await s.unary(r,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,a,o==null?void 0:o.contextValues);return(i=o==null?void 0:o.onHeader)===null||i===void 0||i.call(o,u.header),(l=o==null?void 0:o.onTrailer)===null||l===void 0||l.call(o,u.trailer),u.message}}(e,n);case"server_streaming":return function(s,r){return function(a,o){return Nr(s.stream(r,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,function(i){return Vi(this,arguments,function*(){yield Nt(yield*Yi(Bi(i)))})}([a]),o==null?void 0:o.contextValues),o)}}(e,n);case"client_streaming":return function(s,r){return async function(a,o){var i,l,u,d,m,p;const h=await s.stream(r,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,a,o==null?void 0:o.contextValues);let f;(m=o==null?void 0:o.onHeader)===null||m===void 0||m.call(o,h.header);let _=0;try{for(var y,v=!0,E=za(h.message);!(i=(y=await E.next()).done);v=!0)d=y.value,v=!1,f=d,_++}catch(T){l={error:T}}finally{try{v||i||!(u=E.return)||await u.call(E)}finally{if(l)throw l.error}}if(!f)throw new De("protocol error: missing response message",Le.Unimplemented);if(_>1)throw new De("protocol error: received extra messages for client streaming method",Le.Unimplemented);return(p=o==null?void 0:o.onTrailer)===null||p===void 0||p.call(o,h.trailer),f}}(e,n);case"bidi_streaming":return function(s,r){return function(a,o){return Nr(s.stream(r,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,a,o==null?void 0:o.contextValues),o)}}(e,n);default:return null}})}function Nr(t,e){const n=function(){return Wi(this,arguments,function*(){var s,r;const a=yield ut(t);(s=e==null?void 0:e.onHeader)===null||s===void 0||s.call(e,a.header),yield ut(yield*ji(za(a.message))),(r=e==null?void 0:e.onTrailer)===null||r===void 0||r.call(e,a.trailer)})}()[Symbol.asyncIterator]();return{[Symbol.asyncIterator]:()=>({next:()=>n.next()})}}function zi(t,e,...n){if(n.length>0)throw new Error;return t.services[e]}async function Rr(t){const e=await crypto.subtle.digest("SHA-256",t);return Array.from(new Uint8Array(e)).map(n=>n.toString(16).padStart(2,"0")).join("")}var Xa=(t=>(t.chat="chat",t))(Xa||{}),Ja=(t=>(t.chatMentionFolder="chat-mention-folder",t.chatMentionFile="chat-mention-file",t.chatMentionExternalSource="chat-mention-external-source",t.chatClearContext="chat-clear-context",t.chatRestoreDefaultContext="chat-restore-default-context",t.chatUseActionFind="chat-use-action-find",t.chatUseActionExplain="chat-use-action-explain",t.chatUseActionWriteTest="chat-use-action-write-test",t.chatNewConversation="chat-new-conversation",t.chatEditConversationName="chat-edit-conversation-name",t.chatFailedSmartPasteResolveFile="chat-failed-smart-paste-resolve-file",t.chatPrecomputeSmartPaste="chat-precompute-smart-paste",t.chatSmartPaste="chat-smart-paste",t.chatCodeblockCopy="chat-codeblock-copy",t.chatCodeblockCreate="chat-codeblock-create",t.chatCodeblockGoToFile="chat-codeblock-go-to-file",t.chatCodespanGoToFile="chat-codespan-go-to-file",t.chatCodespanGoToSymbol="chat-codespan-go-to-symbol",t.chatMermaidblockInitialize="chat-mermaidblock-initialize",t.chatMermaidblockToggle="chat-mermaidblock-toggle",t.chatMermaidblockInteract="chat-mermaidblock-interact",t.chatMermaidBlockError="chat-mermaidblock-error",t.chatUseSuggestedQuestion="chat-use-suggested-question",t.chatDisplaySuggestedQuestions="chat-display-suggested-questions",t.setWorkspaceGuidelines="chat-set-workspace-guidelines",t.clearWorkspaceGuidelines="chat-clear-workspace-guidelines",t.setUserGuidelines="chat-set-user-guidelines",t.clearUserGuidelines="chat-clear-user-guidelines",t))(Ja||{});function Cr(t){return t.replace(/^data:.*?;base64,/,"")}async function Mn(t){return new Promise((e,n)=>{const s=new FileReader;s.onload=r=>{var a;return e((a=r.target)==null?void 0:a.result)},s.onerror=n,s.readAsDataURL(t)})}async function On(t){return t.length<1e4?Promise.resolve(function(e){const n=atob(e);return Uint8Array.from(n,s=>s.codePointAt(0)||0)}(t)):new Promise((e,n)=>{const s=new Worker(URL.createObjectURL(new Blob([`
            self.onmessage = function(e) {
              try {
                const base64 = e.data;
                const binString = atob(base64);
                const bytes = new Uint8Array(binString.length);
                for (let i = 0; i < binString.length; i++) {
                  bytes[i] = binString.charCodeAt(i);
                }
                self.postMessage(bytes, [bytes.buffer]);
              } catch (error) {
                self.postMessage({ error: error.message });
              }
            };
            `],{type:"application/javascript"})));s.onmessage=function(r){r.data.error?n(new Error(r.data.error)):e(r.data),s.terminate()},s.onerror=function(r){n(r.error),s.terminate()},s.postMessage(t)})}const Xi=zi(Is("Ci5jbGllbnRzL3NpZGVjYXIvbGlicy9wcm90b3MvdGVzdF9zZXJ2aWNlLnByb3RvEgR0ZXN0IhoKC1Rlc3RSZXF1ZXN0EgsKA2ZvbxgBIAEoCSIeCgxUZXN0UmVzcG9uc2USDgoGcmVzdWx0GAEgASgJMngKC1Rlc3RTZXJ2aWNlEjMKClRlc3RNZXRob2QSES50ZXN0LlRlc3RSZXF1ZXN0GhIudGVzdC5UZXN0UmVzcG9uc2USNAoLRXJyb3JNZXRob2QSES50ZXN0LlRlc3RSZXF1ZXN0GhIudGVzdC5UZXN0UmVzcG9uc2ViBnByb3RvMw"),0);var q=(t=>(t.getEditListRequest="agent-get-edit-list-request",t.getEditListResponse="agent-get-edit-list-response",t.getEditChangesByRequestIdRequest="agent-get-edit-changes-by-request-id-request",t.getEditChangesByRequestIdResponse="agent-get-edit-changes-by-request-id-response",t.setCurrentConversation="agent-set-current-conversation",t.migrateConversationId="agent-migrate-conversation-id",t.revertToTimestamp="revert-to-timestamp",t.chatAgentEditAcceptAll="chat-agent-edit-accept-all",t.reportAgentSessionEvent="report-agent-session-event",t.reportAgentRequestEvent="report-agent-request-event",t.chatReviewAgentFile="chat-review-agent-file",t.getAgentEditContentsByRequestId="get-agent-edit-contents-by-request-id",t.getAgentEditContentsByRequestIdResponse="get-agent-edit-contents-by-request-id-response",t.checkHasEverUsedAgent="check-has-ever-used-agent",t.checkHasEverUsedAgentResponse="check-has-ever-used-agent-response",t.setHasEverUsedAgent="set-has-ever-used-agent",t.checkHasEverUsedRemoteAgent="check-has-ever-used-remote-agent",t.checkHasEverUsedRemoteAgentResponse="check-has-ever-used-remote-agent-response",t.setHasEverUsedRemoteAgent="set-has-ever-used-remote-agent",t.getSoundSettings="get-sound-settings",t.getSoundSettingsResponse="get-sound-settings-response",t.updateSoundSettings="update-sound-settings",t.soundSettingsBroadcast="sound-settings-broadcast",t.getSwarmModeSettings="get-swarm-mode-settings",t.getSwarmModeSettingsResponse="get-swarm-mode-settings-response",t.updateSwarmModeSettings="update-swarm-mode-settings",t.swarmModeSettingsBroadcast="swarm-mode-settings-broadcast",t.getChatModeRequest="get-chat-mode-request",t.getChatModeResponse="get-chat-mode-response",t))(q||{}),Kt=(t=>(t.checkToolCallSafeRequest="check-tool-call-safe-request",t.checkToolCallSafeResponse="check-tool-call-safe-response",t.closeAllToolProcesses="close-all-tool-processes",t.getToolIdentifierRequest="get-tool-identifier-request",t.getToolIdentifierResponse="get-tool-identifier-response",t))(Kt||{}),zt=(t=>(t.loadConversationExchangesRequest="load-conversation-exchanges-request",t.loadConversationExchangesResponse="load-conversation-exchanges-response",t.loadExchangesByUuidsRequest="load-exchanges-by-uuids-request",t.loadExchangesByUuidsResponse="load-exchanges-by-uuids-response",t.saveExchangesRequest="save-exchanges-request",t.saveExchangesResponse="save-exchanges-response",t.deleteExchangesRequest="delete-exchanges-request",t.deleteExchangesResponse="delete-exchanges-response",t.deleteConversationExchangesRequest="delete-conversation-exchanges-request",t.deleteConversationExchangesResponse="delete-conversation-exchanges-response",t.countExchangesRequest="count-exchanges-request",t.countExchangesResponse="count-exchanges-response",t))(zt||{});class Ji{constructor(e=[]){c(this,"_items",[]);c(this,"_focusedItemIdx");c(this,"_subscribers",new Set);c(this,"subscribe",e=>(this._subscribers.add(e),e(this),()=>{this._subscribers.delete(e)}));c(this,"setItems",e=>{this._items=e,this._items.length===0?this.setFocusIdx(void 0):this._focusedItemIdx!==void 0&&this._focusedItemIdx>=this._items.length?this.setFocusIdx(this._items.length-1):this._focusedItemIdx===void 0?this.setFocusIdx(void 0):this.setFocusIdx(this._focusedItemIdx)});c(this,"setFocus",e=>{if(e!==void 0&&e===this.focusedItem)return;const n=e?this._items.indexOf(e):-1;n===-1?this.setFocusIdx(void 0):this.setFocusIdx(n)});c(this,"setFocusIdx",e=>{if(e===this._focusedItemIdx||this._items.length===0)return;if(e===void 0)return this._focusedItemIdx=void 0,void this.notifySubscribers();const n=Math.floor(e/this._items.length)*this._items.length;this._focusedItemIdx=(e-n)%this._items.length,this.notifySubscribers()});c(this,"initFocusIdx",e=>this._focusedItemIdx===void 0&&(this.setFocusIdx(e),!0));c(this,"focusNext",()=>{const e=this.nextIdx();if(e!==void 0)return this.setFocus(this._items[e]),e});c(this,"focusPrev",()=>{const e=this.prevIdx();if(e!==void 0)return this.setFocus(this._items[e]),e});c(this,"prevIdx",(e={})=>{if(this._items.length!==0)return this._focusedItemIdx===void 0?this._items.length-1:e.nowrap&&this._focusedItemIdx===0?0:(this._focusedItemIdx-1+this._items.length)%this._items.length});c(this,"nextIdx",(e={})=>{if(this._items.length!==0)return this._focusedItemIdx===void 0?0:e.nowrap&&this._focusedItemIdx===this._items.length-1?this._items.length-1:(this._focusedItemIdx+1)%this._items.length});c(this,"notifySubscribers",()=>{this._subscribers.forEach(e=>e(this))});this._items=e}get items(){return this._items}get focusedItem(){if(this._focusedItemIdx!==void 0)return this._items[this._focusedItemIdx]}get focusedItemIdx(){return this._focusedItemIdx}}var Zi=(t=>(t[t.unspecified=0]="unspecified",t[t.userGuidelines=1]="userGuidelines",t[t.augmentGuidelines=2]="augmentGuidelines",t[t.rules=3]="rules",t))(Zi||{}),Qi=(t=>(t[t.unspecified=0]="unspecified",t[t.manuallyCreated=1]="manuallyCreated",t[t.auto=2]="auto",t[t.selectedDirectory=3]="selectedDirectory",t[t.selectedFile=4]="selectedFile",t))(Qi||{});function el(t){return t===void 0?{num_lines:-1,num_chars:-1}:{num_lines:t.split(`
`).length,num_chars:t.length}}class Za{constructor(){this.tracingData={flags:{},nums:{},string_stats:{},request_ids:{}}}setFlag(e,n=!0){this.tracingData.flags[e]={value:n,timestamp:new Date().toISOString()}}getFlag(e){const n=this.tracingData.flags[e];return n==null?void 0:n.value}setNum(e,n){this.tracingData.nums[e]={value:n,timestamp:new Date().toISOString()}}getNum(e){const n=this.tracingData.nums[e];return n==null?void 0:n.value}setStringStats(e,n){this.tracingData.string_stats[e]={value:el(n),timestamp:new Date().toISOString()}}setRequestId(e,n){this.tracingData.request_ids[e]={value:n,timestamp:new Date().toISOString()}}}var tl=(t=>(t[t.unspecified=0]="unspecified",t[t.classify_and_distill=1]="classify_and_distill",t[t.orientation=2]="orientation",t))(tl||{}),nl=(t=>(t.start="start",t.end="end",t.memoriesRequestId="memoriesRequestId",t.exceptionThrown="exceptionThrown",t.lastUserExchangeRequestId="lastUserExchangeRequestId",t.noMemoryData="noMemoryData",t.agenticTurnHasRememberToolCall="agenticTurnHasRememberToolCall",t.emptyMemory="emptyMemory",t.removeUserExchangeMemoryFailed="removeUserExchangeMemoryFailed",t))(nl||{});class Qa extends Za{constructor(){super()}static create(){return new Qa}}var sl=(t=>(t.openedAgentConversation="opened-agent-conversation",t.revertCheckpoint="revert-checkpoint",t.agentInterruption="agent-interruption",t.sentUserMessage="sent-user-message",t.rememberToolCall="remember-tool-call",t.openedMemoriesFile="opened-memories-file",t.initialOrientation="initial-orientation",t.classifyAndDistill="classify-and-distill",t.flushMemories="flush-memories",t.vsCodeTerminalCwdNotAbsolute="vs-code-terminal-cwd-not-absolute",t.vsCodeTerminalCwdDoesNotExist="vs-code-terminal-cwd-does-not-exist",t.vsCodeTerminalShellIntegrationNotAvailable="vs-code-terminal-shell-integration-not-available",t.vsCodeTerminalReadingApproximateOutput="vs-code-terminal-reading-approximate-output",t.vsCodeTerminalTimedOutWaitingForNoopCommand="vs-code-terminal-timed-out-waiting-for-noop-command",t.vsCodeTerminalTimedOutWaitingForSetCwdCommand="vs-code-terminal-timed-out-waiting-for-set-cwd-command",t.vsCodeTerminalFailedToUseShellIntegration="vs-code-terminal-failed-to-use-shell-integration",t.vsCodeTerminalLastCommandIsSameAsCurrent="vs-code-terminal-last-command-is-same-as-current",t.vsCodeTerminalPollingDeterminedProcessIsDone="vs-code-terminal-polling-determined-process-is-done",t.vsCodeScriptStrategyPollingDeterminedProcessIsDone="vs-code-script-strategy-polling-determined-process-is-done",t.vsCodeTerminalFailedToReadOutput="vs-code-terminal-failed-to-read-output",t.vsCodeTerminalBuggyOutput="vs-code-terminal-buggy-output",t.vsCodeTerminalBuggyExecutionEvents="vs-code-terminal-buggy-execution-events",t.vsCodeTerminalUnsupportedVSCodeShell="vs-code-terminal-unsupported-vscode-shell",t.vsCodeTerminalFailedToFindGitBash="vs-code-terminal-failed-to-find-git-bash",t.vsCodeTerminalFailedToFindPowerShell="vs-code-terminal-failed-to-find-powershell",t.vsCodeTerminalNoSupportedShellsFound="vs-code-terminal-no-supported-shells-found",t.vsCodeTerminalSettingsChanged="vs-code-terminal-settings-changed",t.vsCodeTerminalWaitTimeout="vs-code-terminal-wait-timeout",t.vsCodeTerminalErrorLoadingSettings="vs-code-terminal-error-loading-settings",t.vsCodeTerminalErrorCheckingForShellUpdates="vs-code-terminal-error-checking-for-shell-updates",t.vsCodeTerminalErrorCleaningUpTempDir="vs-code-terminal-error-cleaning-up-temp-dir",t.vsCodeTerminalErrorInitializingShells="vs-code-terminal-error-initializing-shells",t.vsCodeTerminalErrorCheckingShellCapability="vs-code-terminal-error-checking-shell-capability",t.vsCodeTerminalErrorCreatingZshEnvironment="vs-code-terminal-error-creating-zsh-environment",t.vsCodeTerminalMissedStartEvent="vs-code-terminal-missed-start-event",t.vsCodeTerminalReadStreamTimeoutWhenProcessIsComplete="vs-code-terminal-read-stream-timeout-when-process-is-complete",t.vsCodeTerminalScriptCommandNotAvailable="vs-code-terminal-script-command-not-available",t.enhancedPrompt="enhanced-prompt",t.memoriesMove="memories-move",t.rulesImported="rules-imported",t.taskListUsage="task-list-usage",t.memoryUsage="memory-usage",t.contentTruncation="content-truncation",t))(sl||{}),sn=(t=>(t.sentUserMessage="sent-user-message",t.chatHistorySummarization="chat-history-summarization",t.enhancedPrompt="enhanced-prompt",t.firstTokenReceived="first-token-received",t.chatHistoryTruncated="chat-history-truncated",t))(sn||{}),rl=(t=>(t.memoriesRequestId="memoriesRequestId",t.exceptionThrown="exceptionThrown",t.start="start",t.end="end",t.noPendingUserMessage="noPendingUserMessage",t.startSendSilentExchange="startSendSilentExchange",t.sendSilentExchangeRequestId="sendSilentExchangeRequestId",t.sendSilentExchangeResponseStats="sendSilentExchangeResponseStats",t.noRequestId="noRequestId",t.conversationChanged="conversationChanged",t.explanationStats="explanationStats",t.contentStats="contentStats",t.invalidResponse="invalidResponse",t.worthRemembering="worthRemembering",t.lastUserExchangeRequestId="lastUserExchangeRequestId",t.noLastUserExchangeRequestId="noLastUserExchangeRequestId",t))(rl||{});class eo extends Za{constructor(){super()}static create(){return new eo}}var al=(t=>(t.remoteAgentSetup="remote-agent-setup",t.setupScript="setup-script",t.sshInteraction="ssh-interaction",t.notificationBell="notification-bell",t.diffPanel="diff-panel",t.setupPageOpened="setup-page-opened",t.githubAPIFailure="github-api-failure",t.remoteAgentCreated="remote-agent-created",t.changesApplied="changes-applied",t.createdPR="created-pr",t.modeSelector="mode-selector",t.remoteAgentSetupWindow="remote-agent-setup-window",t.remoteAgentThreadList="remote-agent-thread-list",t.remoteAgentNewThreadButton="remote-agent-new-thread-button",t))(al||{}),ol=(t=>(t[t.unknownSourceControl=0]="unknownSourceControl",t[t.git=1]="git",t[t.github=2]="github",t))(ol||{}),il=(t=>(t[t.unknownMode=0]="unknownMode",t[t.chat=1]="chat",t[t.agent=2]="agent",t[t.remoteAgent=3]="remoteAgent",t))(il||{}),ll=(t=>(t[t.unknownModeSelectorAction=0]="unknownModeSelectorAction",t[t.open=1]="open",t[t.close=2]="close",t[t.select=3]="select",t[t.init=4]="init",t))(ll||{}),ul=(t=>(t[t.unknownSetupWindowAction=0]="unknownSetupWindowAction",t[t.open=1]="open",t[t.close=2]="close",t[t.selectRepo=3]="selectRepo",t[t.selectBranch=4]="selectBranch",t[t.selectSetupScript=5]="selectSetupScript",t[t.autoGenerateSetupScript=6]="autoGenerateSetupScript",t[t.manuallyCreateSetupScript=7]="manuallyCreateSetupScript",t[t.typeInPromptWindow=8]="typeInPromptWindow",t[t.clickRewritePrompt=9]="clickRewritePrompt",t[t.enableNotifications=10]="enableNotifications",t[t.disableNotifications=11]="disableNotifications",t[t.clickCreateAgent=12]="clickCreateAgent",t))(ul||{}),cl=(t=>(t[t.unknownAgentListAction=0]="unknownAgentListAction",t[t.open=1]="open",t[t.close=2]="close",t[t.selectAgent=3]="selectAgent",t[t.deleteAgent=4]="deleteAgent",t[t.pinAgent=5]="pinAgent",t[t.unpinAgent=6]="unpinAgent",t))(cl||{}),dl=(t=>(t[t.unknown=0]="unknown",t[t.click=1]="click",t[t.open=2]="open",t[t.close=3]="close",t))(dl||{}),ml=(t=>(t[t.unknown=0]="unknown",t[t.chat=1]="chat",t[t.agent=2]="agent",t[t.remoteAgent=3]="remoteAgent",t))(ml||{}),hl=(t=>(t[t.unknown=0]="unknown",t[t.addTask=1]="addTask",t[t.addSubtask=2]="addSubtask",t[t.updateTaskStatus=3]="updateTaskStatus",t[t.updateTaskName=4]="updateTaskName",t[t.updateTaskDescription=5]="updateTaskDescription",t[t.reorganizeTaskList=6]="reorganizeTaskList",t[t.deleteTask=7]="deleteTask",t[t.runSingleTask=8]="runSingleTask",t[t.runAllTasks=9]="runAllTasks",t[t.viewTaskList=10]="viewTaskList",t[t.exportTaskList=11]="exportTaskList",t[t.importTaskList=12]="importTaskList",t[t.syncTaskList=13]="syncTaskList",t))(hl||{}),pl=(t=>(t[t.unknown=0]="unknown",t[t.user=1]="user",t[t.agent=2]="agent",t))(pl||{}),gl=(t=>(t[t.unknown=0]="unknown",t[t.saveMemory=1]="saveMemory",t[t.discardMemory=2]="discardMemory",t[t.editMemory=3]="editMemory",t[t.viewMemories=4]="viewMemories",t[t.refreshMemories=5]="refreshMemories",t[t.filterByState=6]="filterByState",t[t.filterByVersion=7]="filterByVersion",t[t.openMemoriesFile=8]="openMemoriesFile",t[t.createMemory=9]="createMemory",t))(gl||{}),fl=(t=>(t[t.unknown=0]="unknown",t[t.user=1]="user",t[t.agent=2]="agent",t))(fl||{});function yl(t,e,n=1e3){let s=null,r=0;const a=St(e),o=()=>{const i=(()=>{const l=Date.now();if(s!==null&&l-r<n)return s;const u=t();return s=u,r=l,u})();a.set(i)};return{subscribe:a.subscribe,resetCache:()=>{s=null,o()},updateStore:o}}var to=(t=>(t[t.unset=0]="unset",t[t.positive=1]="positive",t[t.negative=2]="negative",t))(to||{}),_l=(t=>(t.longRunning="longRunning",t.running="running",t.done="done",t))(_l||{}),bl=(t=>(t.initializing="initializing",t.enabled="enabled",t.disabled="disabled",t.partial="partial",t))(bl||{});const xs=class xs{static hasFrontmatter(e){return this.frontmatterRegex.test(e)}static extractFrontmatter(e){const n=e.match(this.frontmatterRegex);return n&&n[1]?n[1]:null}static extractContent(e){return e.replace(this.frontmatterRegex,"")}static parseBoolean(e,n,s=!0){const r=this.extractFrontmatter(e);if(r){const a=new RegExp(`${n}\\s*:\\s*(true|false)`,"i"),o=r.match(a);if(o&&o[1])return o[1].toLowerCase()==="true"}return s}static parseString(e,n,s=""){const r=this.extractFrontmatter(e);if(r){const a=new RegExp(`${n}\\s*:\\s*["']?([^"'
]*)["']?`,"i"),o=r.match(a);if(o&&o[1])return o[1].trim()}return s}static updateFrontmatter(e,n,s){const r=e.match(this.frontmatterRegex),a=typeof s!="string"||/^(true|false)$/.test(s.toLowerCase())?String(s):`"${s}"`;if(r){const o=r[1],i=new RegExp(`(${n}\\s*:\\s*)([^\\n]*)`,"i");if(o.match(i)){const l=o.replace(i,`$1${a}`);return e.replace(this.frontmatterRegex,`---
${l}---
`)}{const l=`${o.endsWith(`
`)?o:o+`
`}${n}: ${a}
`;return e.replace(this.frontmatterRegex,`---
${l}---
`)}}return`---
${n}: ${a}
---

${e}`}static createFrontmatter(e,n){let s=e;this.hasFrontmatter(s)&&(s=this.extractContent(s));for(const[r,a]of Object.entries(n))s=this.updateFrontmatter(s,r,a);return s}};xs.frontmatterRegex=/^---\s*\n([\s\S]*?)\n---\s*\n/;let re=xs;const Be=class Be{static parseRuleFile(e,n){const s=re.parseString(e,this.DESCRIPTION_FRONTMATTER_KEY,""),r=re.extractContent(e);return{type:this.getRuleTypeFromContent(e),path:n,content:r,description:s||void 0}}static formatRuleFileForMarkdown(e){let n=e.content;return n=re.updateFrontmatter(n,this.TYPE_FRONTMATTER_KEY,this.mapRuleTypeToString(e.type)),e.description&&(n=re.updateFrontmatter(n,this.DESCRIPTION_FRONTMATTER_KEY,e.description)),n}static getAlwaysApplyFrontmatterKey(e){return re.parseBoolean(e,this.ALWAYS_APPLY_FRONTMATTER_KEY,!1)}static extractContent(e){return re.extractContent(e)}static updateAlwaysApplyFrontmatterKey(e,n){return re.updateFrontmatter(e,this.ALWAYS_APPLY_FRONTMATTER_KEY,n)}static getDescriptionFrontmatterKey(e){return re.parseString(e,this.DESCRIPTION_FRONTMATTER_KEY,"")}static updateDescriptionFrontmatterKey(e,n){return re.updateFrontmatter(e,this.DESCRIPTION_FRONTMATTER_KEY,n)}static mapStringToRuleType(e){switch(e.toLowerCase()){case"always_apply":return le.ALWAYS_ATTACHED;case"manual":return le.MANUAL;case"agent_requested":return le.AGENT_REQUESTED;default:return this.DEFAULT_RULE_TYPE}}static mapRuleTypeToString(e){switch(e){case le.ALWAYS_ATTACHED:return"always_apply";case le.MANUAL:return"manual";case le.AGENT_REQUESTED:return"agent_requested";default:return"manual"}}static isValidTypeValue(e){return this.VALID_TYPE_VALUES.includes(e.toLowerCase())}static getTypeFrontmatterKey(e){return re.parseString(e,this.TYPE_FRONTMATTER_KEY,"")}static updateTypeFrontmatterKey(e,n){const s=this.mapRuleTypeToString(n);return re.updateFrontmatter(e,this.TYPE_FRONTMATTER_KEY,s)}static getRuleTypeFromContent(e){const n=this.getTypeFrontmatterKey(e);if(n&&this.isValidTypeValue(n))return this.mapStringToRuleType(n);const s=this.getAlwaysApplyFrontmatterKey(e),r=this.getDescriptionFrontmatterKey(e);return s?le.ALWAYS_ATTACHED:r&&r.trim()!==""?le.AGENT_REQUESTED:le.MANUAL}};Be.ALWAYS_APPLY_FRONTMATTER_KEY="alwaysApply",Be.DESCRIPTION_FRONTMATTER_KEY="description",Be.TYPE_FRONTMATTER_KEY="type",Be.VALID_TYPE_VALUES=["always_apply","manual","agent_requested"],Be.DEFAULT_RULE_TYPE=le.MANUAL;let Ye=Be;const Qe=".augment",gt="rules",Dn=".augment-guidelines";function Z(t,e){return e in t&&t[e]!==void 0}function vl(t){return Z(t,"file")}function El(t){return Z(t,"recentFile")}function Tl(t){return Z(t,"folder")}function Sl(t){return Z(t,"sourceFolder")}function Lc(t){return Z(t,"sourceFolderGroup")}function $c(t){return Z(t,"selection")}function Il(t){return Z(t,"externalSource")}function qc(t){return Z(t,"allDefaultContext")}function Hc(t){return Z(t,"clearContext")}function Gc(t){return Z(t,"userGuidelines")}function Bc(t){return Z(t,"agentMemories")}function no(t){return Z(t,"personality")}function wl(t){return Z(t,"rule")}function Nl(t){return Z(t,"task")}const Vc={allDefaultContext:!0,label:"Default Context",id:"allDefaultContext"},Yc={clearContext:!0,label:"Clear Context",id:"clearContext"},jc={userGuidelines:{overLimit:!1,contents:"",lengthLimit:2e3},label:"User Guidelines",id:"userGuidelines"},Wc={agentMemories:{},label:"Agent Memories",id:"agentMemories"},kr=[{personality:{type:V.DEFAULT,description:"Expert software engineer - trusted coding agent, at your service!"},label:"Agent Auggie",name:"auggie-personality-agent-default",id:"auggie-personality-agent-default"},{personality:{type:V.PROTOTYPER,description:"Fast and loose - let's get it done, boss!"},label:"Prototyper Auggie",name:"auggie-personality-prototyper",id:"auggie-personality-prototyper"},{personality:{type:V.BRAINSTORM,description:"Thoughtful and creative - thinking through all possibilities..."},label:"Brainstorm Auggie",name:"auggie-personality-brainstorm",id:"auggie-personality-brainstorm"},{personality:{type:V.REVIEWER,description:"Code detective - finding issues and analyzing implications"},label:"Reviewer Auggie",name:"auggie-personality-reviewer",id:"auggie-personality-reviewer"}];function Kc(t){return Z(t,"group")}function zc(t){const e=new Map;return t.forEach(n=>{vl(n)?e.set("file",[...e.get("file")??[],n]):El(n)?e.set("recentFile",[...e.get("recentFile")??[],n]):Tl(n)?e.set("folder",[...e.get("folder")??[],n]):Il(n)?e.set("externalSource",[...e.get("externalSource")??[],n]):Sl(n)?e.set("sourceFolder",[...e.get("sourceFolder")??[],n]):no(n)?e.set("personality",[...e.get("personality")??[],n]):wl(n)&&e.set("rule",[...e.get("rule")??[],n])}),[{label:"Personalities",id:"personalities",group:{type:"personality",materialIcon:"person",items:e.get("personality")??[]}},{label:"Files",id:"files",group:{type:"file",materialIcon:"insert_drive_file",items:e.get("file")??[]}},{label:"Folders",id:"folders",group:{type:"folder",materialIcon:"folder",items:e.get("folder")??[]}},{label:"Source Folders",id:"sourceFolders",group:{type:"sourceFolder",materialIcon:"folder_managed",items:e.get("sourceFolder")??[]}},{label:"Recently Opened Files",id:"recentlyOpenedFiles",group:{type:"recentFile",materialIcon:"insert_drive_file",items:e.get("recentFile")??[]}},{label:"Documentation",id:"externalSources",group:{type:"externalSource",materialIcon:"link",items:e.get("externalSource")??[]}},{label:"Rules",id:"rules",group:{type:"rule",materialIcon:"rule",items:e.get("rule")??[]}}].filter(n=>n.group.items.length>0)}function Rl(t){const e=(n={rootPath:t.repoRoot,relPath:t.pathName}).rootPath+"/"+n.relPath;var n;const s={label:Ro(t.pathName).split("/").filter(r=>r.trim()!=="").pop()||"",name:e,id:e};if(t.fullRange){const r=`:L${t.fullRange.startLineNumber}-${t.fullRange.endLineNumber}`;s.label+=r,s.name+=r,s.id+=r}else if(t.range){const r=`:L${t.range.start}-${t.range.stop}`;s.label+=r,s.name+=r,s.id+=r}return s}function Cl(t){const e=t.path.split("/"),n=e[e.length-1],s=n.endsWith(".md")?n.slice(0,-3):n,r=`${Qe}/${gt}/${t.path}`;return{label:s,name:r,id:r}}var M=(t=>(t[t.unknown=0]="unknown",t[t.new=1]="new",t[t.checkingSafety=2]="checkingSafety",t[t.runnable=3]="runnable",t[t.running=4]="running",t[t.completed=5]="completed",t[t.error=6]="error",t[t.cancelling=7]="cancelling",t[t.cancelled=8]="cancelled",t))(M||{});function Fn(t){return`${t.requestId};${t.toolUseId}`}function xr(t){const[e,n]=t.split(";");return{requestId:e,toolUseId:n}}var kl=(t=>(t.readFile="read-file",t.saveFile="save-file",t.editFile="edit-file",t.clarify="clarify",t.onboardingSubAgent="onboarding-sub-agent",t.launchProcess="launch-process",t.killProcess="kill-process",t.readProcess="read-process",t.writeProcess="write-process",t.listProcesses="list-processes",t.waitProcess="wait-process",t.openBrowser="open-browser",t.strReplaceEditor="str-replace-editor",t.remember="remember",t.diagnostics="diagnostics",t.setupScript="setup-script",t.readTerminal="read-terminal",t.gitCommitRetrieval="git-commit-retrieval",t.memoryRetrieval="memory-retrieval",t.startWorkerAgent="start_worker_agent",t.readWorkerState="read_worker_state",t.waitForWorkerAgent="wait_for_worker_agent",t.sendInstructionToWorkerAgent="send_instruction_to_worker_agent",t.stopWorkerAgent="stop_worker_agent",t.deleteWorkerAgent="delete_worker_agent",t.readWorkerAgentEdits="read_worker_agent_edits",t.applyWorkerAgentEdits="apply_worker_agent_edits",t.LocalSubAgent="local-sub-agent",t))(kl||{}),xl=(t=>(t.remoteToolHost="remoteToolHost",t.localToolHost="localToolHost",t.sidecarToolHost="sidecarToolHost",t.mcpHost="mcpHost",t))(xl||{}),rn=(t=>(t[t.ContentText=0]="ContentText",t[t.ContentImage=1]="ContentImage",t))(rn||{}),Al=(t=>(t[t.Unsafe=0]="Unsafe",t[t.Safe=1]="Safe",t[t.Check=2]="Check",t))(Al||{}),Ml=(t=>(t[t.Unknown=0]="Unknown",t[t.WebSearch=1]="WebSearch",t[t.GitHubApi=8]="GitHubApi",t[t.Linear=12]="Linear",t[t.Jira=13]="Jira",t[t.Confluence=14]="Confluence",t[t.Notion=15]="Notion",t[t.Supabase=16]="Supabase",t[t.Glean=17]="Glean",t))(Ml||{});function Ar(t,e){return function(n,s){if(n.length<=s||n.length===0)return{truncatedText:n};const r=n.split(`
`),a="... additional lines truncated ..."+(r[0].endsWith("\r")?"\r":"");let o,i="";if(r.length<2||r[0].length+r[r.length-1].length+a.length>s){const l=Math.floor(s/2);i=[n.slice(0,l),"<...>",n.slice(-l)].join(""),o=[1,1,r.length,r.length]}else{const l=[],u=[];let d=a.length+1;for(let m=0;m<Math.floor(r.length/2);m++){const p=r[m],h=r[r.length-1-m],f=p.length+h.length+2;if(d+f>s)break;d+=f,l.push(p),u.push(h)}o=[1,l.length,r.length-u.length+1,r.length],l.push(a),l.push(...u.reverse()),i=l.join(`
`)}return{truncatedText:i,shownRangeWhenTruncated:o}}(t,e).truncatedText}function Ol(t){var n;if(!t)return Mt.IMAGE_FORMAT_UNSPECIFIED;switch((n=t.split("/")[1])==null?void 0:n.toLowerCase()){case"jpeg":case"jpg":return Mt.JPEG;case"png":return Mt.PNG;default:return Mt.IMAGE_FORMAT_UNSPECIFIED}}function Dl(t,e,n){var r,a;if(t.phase!==M.cancelled&&t.phase!==M.completed&&t.phase!==M.error)return;let s;return(r=t.result)!=null&&r.contentNodes?(s=function(o,i){return o.map(l=>l.type===rn.ContentText?{type:Tn.CONTENT_TEXT,text_content:l.text_content}:l.type===rn.ContentImage&&l.image_content&&i?{type:Tn.CONTENT_IMAGE,image_content:{image_data:l.image_content.image_data,format:Ol(l.image_content.media_type)}}:{type:Tn.CONTENT_TEXT,text_content:"[Error: Invalid content node]"})}(t.result.contentNodes,n),{content:"",is_error:t.result.isError,request_id:t.result.requestId,tool_use_id:e,content_nodes:s}):((a=t.result)==null?void 0:a.text)!==void 0?{content:t.result.text,is_error:t.result.isError,request_id:t.result.requestId,tool_use_id:e}:void 0}function Fl(t=[]){let e;for(const n of t){if(n.type===C.TOOL_USE)return n;n.type===C.TOOL_USE_START&&(e=n)}return e}function Ul(t,e,n,s){if(!t||!e)return[];let r=!1;return e.filter(a=>{var i;const o=s!=null&&s.isActive&&a.tool_use?s.getToolUseState(a.tool_use.tool_use_id):n.getToolUseState(a.requestId??t,(i=a.tool_use)==null?void 0:i.tool_use_id);return r===!1&&o.phase!==M.new&&o.phase!==M.unknown&&o.phase!==M.checkingSafety&&a.tool_use!==void 0||(o.phase===M.runnable&&(r=!0),!1)})}function Xc(t,e){if(t.contentNodes&&t.contentNodes.length>0){const n=t.contentNodes.map(s=>{if(s.type===rn.ContentText){let r="";return s.text_content&&(r=Ar(s.text_content,e/t.contentNodes.length)),{...s,text_content:r}}return s});return{...t,contentNodes:n}}return{...t,text:Ar(t.text,e)}}const Pl="__NEW_AGENT__",Jc=t=>t.chatItemType===void 0,Zc=(t,e)=>{var a;const n=t.chatHistory.at(-1);if(!n||!L(n))return Ge.notRunning;if(!(n.status===I.success||n.status===I.failed||n.status===I.cancelled))return Ge.running;const s=((a=n.structured_output_nodes)==null?void 0:a.filter(o=>o.type===C.TOOL_USE&&!!o.tool_use))??[];let r;if(r=e.enableParallelTools?Ul(n.request_id,s,t).at(-1):s.at(-1),!r||!r.tool_use)return Ge.notRunning;switch(t.getToolUseState(n.request_id,r.tool_use.tool_use_id).phase){case M.runnable:return Ge.awaitingUserAction;case M.cancelled:return Ge.notRunning;default:return Ge.running}},ts=t=>L(t)&&!!t.request_message,Ll=t=>t.chatHistory.findLast(e=>ts(e)),Qc=(t,e)=>{const n=Ll(t);return n!=null&&n.request_id?t.historyFrom(n.request_id,!0).filter(s=>L(s)&&(!e||e(s))):[]},ed=t=>{var s;const e=t.chatHistory.at(-1);if(!(e!=null&&e.request_id)||!L(e))return!1;const n=((s=e.structured_output_nodes)==null?void 0:s.filter(r=>r.type===C.TOOL_USE))??[];for(const r of n)if(r.tool_use&&t.getToolUseState(e.request_id,r.tool_use.tool_use_id).phase===M.runnable)return t.updateToolUseState({requestId:e.request_id,toolUseId:r.tool_use.tool_use_id,phase:M.cancelled}),!0;return!1};function $l(t,e){const n=t.customPersonalityPrompts;if(n)switch(e){case V.DEFAULT:if(n.agent&&n.agent.trim()!=="")return n.agent;break;case V.PROTOTYPER:if(n.prototyper&&n.prototyper.trim()!=="")return n.prototyper;break;case V.BRAINSTORM:if(n.brainstorm&&n.brainstorm.trim()!=="")return n.brainstorm;break;case V.REVIEWER:if(n.reviewer&&n.reviewer.trim()!=="")return n.reviewer}return ql[e]}const ql={[V.DEFAULT]:`
# Agent Auggie Personality Description
You are Augment Agent, an agentic coding AI assistant.
Focus on helping the user with their coding tasks efficiently.

## Rules:
- You have no restrictions on the tools you may use
- Follow the original system instructions
  `,[V.PROTOTYPER]:`
# Prototyper Auggie Personality Description
You are Prototyper Auggie, an agentic coding AI assistant focused on building prototypes and visual applications.

## Your approach:
- Be fast and action-oriented
- Implement things quickly to show results
- Open webpages to demonstrate functionality
- Focus on building something visual and interactive
- Use modern frameworks and tools to create working prototypes
- Prioritize getting a working demo over perfect architecture
- Show progress frequently with visual results
- Prefer to act and run tools, rather than asking for permission
- Only ask for permission if there is something potentially very dangerous or irreversible

## Implementation preferences:
- When user does not specify which frameworks to use, default to modern frameworks, e.g. React with vite or next.js
- Initialize projects using CLI tools instead of writing from scratch
- For database and auth, use Supabase as a good default option
- Before using open-browser to show the app, use curl to check for errors
- Remember that modern frameworks have hot reload, so avoid calling open-browser multiple times

## Rules:
- For extremely destructive or irreversible actions, you should ask for permission
- For other tasks, you must proceed without asking for permission
  `,[V.BRAINSTORM]:`
# Brainstorm Auggie Personality Description
You are Brainstorm Auggie, an agentic coding AI assistant focused on planning and brainstorming solutions.

## Your approach:
- Be slow, careful, and thorough in your analysis
- Look through all upstream/downstream APIs to understand implications
- Focus on finding a comprehensive plan that solves the user's query
- Do not run commands, create code, or implement solutions directly
- Your job is to be introspective and think deeply about the problem
- Brainstorm multiple approaches and evaluate their tradeoffs
- Consider edge cases and potential issues with each approach

## Planning preferences:
- Analyze the codebase thoroughly before suggesting changes
- Consider multiple implementation options with pros and cons
- Identify potential risks and challenges for each approach
- Create detailed, step-by-step plans for implementation
- Provide reasoning for architectural decisions
- Consider performance, maintainability, and scalability
- Do not execute the plan - your role is to provide guidance only

## Rules:
- Prefer information gathering and non-destructive tools
- Prefer non-destructive and non-modifying tools
- You must never execute code, modify the codebase, or make changes
- Consider using Mermaid diagrams to help visualize complex concepts
- Once you have a proposal, please examine it critically, and do a revision before finalizing
  `,[V.REVIEWER]:`
# Reviewer Auggie Personality Description
You are Reviewer Auggie, an agentic coding AI assistant focused on reviewing code changes and identifying potential issues.

## Your approach:
- Act like a code detective to find potential bugs and issues
- Use git commands to analyze changes against the merge base
- Be super inquisitive and look for anything suspicious
- Build a mental model of what is happening in the code change
- Analyze API implications and downstream effects
- Guard the codebase from potential negative side effects
- Focus on understanding the changes from first principles

## Review preferences:
- Use git and GitHub tools to get code history information
- Compare changes against the logical base or merge base
- Look for edge cases and potential bugs
- Analyze API contracts and potential breaking changes
- Consider performance implications
- Check for security vulnerabilities
- Verify test coverage for the changes

## Rules:
- Use git commands and GitHub API to analyze code changes
- Be thorough and methodical in your analysis
- Focus on finding potential issues rather than implementing solutions
- Provide constructive feedback with specific examples
- Consider both the technical implementation and the broader impact
  `};var oe=(t=>(t.NOT_STARTED="NOT_STARTED",t.IN_PROGRESS="IN_PROGRESS",t.CANCELLED="CANCELLED",t.COMPLETE="COMPLETE",t))(oe||{}),Cs=(t=>(t.USER="USER",t.AGENT="AGENT",t))(Cs||{}),so={},an={},on={};let Ut;Object.defineProperty(on,"__esModule",{value:!0}),on.default=function(){if(!Ut&&(Ut=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!Ut))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Ut(Hl)};const Hl=new Uint8Array(16);var $e={},Ke={},ln={};Object.defineProperty(ln,"__esModule",{value:!0}),ln.default=void 0;ln.default=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,Object.defineProperty(Ke,"__esModule",{value:!0}),Ke.default=void 0;var Pt,Gl=(Pt=ln)&&Pt.__esModule?Pt:{default:Pt},Bl=function(t){return typeof t=="string"&&Gl.default.test(t)};Ke.default=Bl,Object.defineProperty($e,"__esModule",{value:!0}),$e.default=void 0,$e.unsafeStringify=ro;var Vl=function(t){return t&&t.__esModule?t:{default:t}}(Ke);const H=[];for(let t=0;t<256;++t)H.push((t+256).toString(16).slice(1));function ro(t,e=0){return H[t[e+0]]+H[t[e+1]]+H[t[e+2]]+H[t[e+3]]+"-"+H[t[e+4]]+H[t[e+5]]+"-"+H[t[e+6]]+H[t[e+7]]+"-"+H[t[e+8]]+H[t[e+9]]+"-"+H[t[e+10]]+H[t[e+11]]+H[t[e+12]]+H[t[e+13]]+H[t[e+14]]+H[t[e+15]]}var Yl=function(t,e=0){const n=ro(t,e);if(!(0,Vl.default)(n))throw TypeError("Stringified UUID is invalid");return n};$e.default=Yl,Object.defineProperty(an,"__esModule",{value:!0}),an.default=void 0;var jl=function(t){return t&&t.__esModule?t:{default:t}}(on),Wl=$e;let Mr,Un,Pn=0,Ln=0;var Kl=function(t,e,n){let s=e&&n||0;const r=e||new Array(16);let a=(t=t||{}).node||Mr,o=t.clockseq!==void 0?t.clockseq:Un;if(a==null||o==null){const p=t.random||(t.rng||jl.default)();a==null&&(a=Mr=[1|p[0],p[1],p[2],p[3],p[4],p[5]]),o==null&&(o=Un=16383&(p[6]<<8|p[7]))}let i=t.msecs!==void 0?t.msecs:Date.now(),l=t.nsecs!==void 0?t.nsecs:Ln+1;const u=i-Pn+(l-Ln)/1e4;if(u<0&&t.clockseq===void 0&&(o=o+1&16383),(u<0||i>Pn)&&t.nsecs===void 0&&(l=0),l>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");Pn=i,Ln=l,Un=o,i+=122192928e5;const d=(1e4*(268435455&i)+l)%4294967296;r[s++]=d>>>24&255,r[s++]=d>>>16&255,r[s++]=d>>>8&255,r[s++]=255&d;const m=i/4294967296*1e4&268435455;r[s++]=m>>>8&255,r[s++]=255&m,r[s++]=m>>>24&15|16,r[s++]=m>>>16&255,r[s++]=o>>>8|128,r[s++]=255&o;for(let p=0;p<6;++p)r[s+p]=a[p];return e||(0,Wl.unsafeStringify)(r)};an.default=Kl;var un={},Fe={},Rt={};Object.defineProperty(Rt,"__esModule",{value:!0}),Rt.default=void 0;var zl=function(t){return t&&t.__esModule?t:{default:t}}(Ke),Xl=function(t){if(!(0,zl.default)(t))throw TypeError("Invalid UUID");let e;const n=new Uint8Array(16);return n[0]=(e=parseInt(t.slice(0,8),16))>>>24,n[1]=e>>>16&255,n[2]=e>>>8&255,n[3]=255&e,n[4]=(e=parseInt(t.slice(9,13),16))>>>8,n[5]=255&e,n[6]=(e=parseInt(t.slice(14,18),16))>>>8,n[7]=255&e,n[8]=(e=parseInt(t.slice(19,23),16))>>>8,n[9]=255&e,n[10]=(e=parseInt(t.slice(24,36),16))/1099511627776&255,n[11]=e/4294967296&255,n[12]=e>>>24&255,n[13]=e>>>16&255,n[14]=e>>>8&255,n[15]=255&e,n};Rt.default=Xl,Object.defineProperty(Fe,"__esModule",{value:!0}),Fe.URL=Fe.DNS=void 0,Fe.default=function(t,e,n){function s(r,a,o,i){var l;if(typeof r=="string"&&(r=function(d){d=unescape(encodeURIComponent(d));const m=[];for(let p=0;p<d.length;++p)m.push(d.charCodeAt(p));return m}(r)),typeof a=="string"&&(a=(0,Zl.default)(a)),((l=a)===null||l===void 0?void 0:l.length)!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let u=new Uint8Array(16+r.length);if(u.set(a),u.set(r,a.length),u=n(u),u[6]=15&u[6]|e,u[8]=63&u[8]|128,o){i=i||0;for(let d=0;d<16;++d)o[i+d]=u[d];return o}return(0,Jl.unsafeStringify)(u)}try{s.name=t}catch{}return s.DNS=ao,s.URL=oo,s};var Jl=$e,Zl=function(t){return t&&t.__esModule?t:{default:t}}(Rt);const ao="6ba7b810-9dad-11d1-80b4-00c04fd430c8";Fe.DNS=ao;const oo="6ba7b811-9dad-11d1-80b4-00c04fd430c8";Fe.URL=oo;var cn={};function Or(t){return 14+(t+64>>>9<<4)+1}function Ue(t,e){const n=(65535&t)+(65535&e);return(t>>16)+(e>>16)+(n>>16)<<16|65535&n}function bn(t,e,n,s,r,a){return Ue((o=Ue(Ue(e,t),Ue(s,a)))<<(i=r)|o>>>32-i,n);var o,i}function Y(t,e,n,s,r,a,o){return bn(e&n|~e&s,t,e,r,a,o)}function j(t,e,n,s,r,a,o){return bn(e&s|n&~s,t,e,r,a,o)}function W(t,e,n,s,r,a,o){return bn(e^n^s,t,e,r,a,o)}function K(t,e,n,s,r,a,o){return bn(n^(e|~s),t,e,r,a,o)}Object.defineProperty(cn,"__esModule",{value:!0}),cn.default=void 0;var Ql=function(t){if(typeof t=="string"){const e=unescape(encodeURIComponent(t));t=new Uint8Array(e.length);for(let n=0;n<e.length;++n)t[n]=e.charCodeAt(n)}return function(e){const n=[],s=32*e.length,r="0123456789abcdef";for(let a=0;a<s;a+=8){const o=e[a>>5]>>>a%32&255,i=parseInt(r.charAt(o>>>4&15)+r.charAt(15&o),16);n.push(i)}return n}(function(e,n){e[n>>5]|=128<<n%32,e[Or(n)-1]=n;let s=1732584193,r=-271733879,a=-1732584194,o=271733878;for(let i=0;i<e.length;i+=16){const l=s,u=r,d=a,m=o;s=Y(s,r,a,o,e[i],7,-680876936),o=Y(o,s,r,a,e[i+1],12,-389564586),a=Y(a,o,s,r,e[i+2],17,606105819),r=Y(r,a,o,s,e[i+3],22,-1044525330),s=Y(s,r,a,o,e[i+4],7,-176418897),o=Y(o,s,r,a,e[i+5],12,1200080426),a=Y(a,o,s,r,e[i+6],17,-1473231341),r=Y(r,a,o,s,e[i+7],22,-45705983),s=Y(s,r,a,o,e[i+8],7,1770035416),o=Y(o,s,r,a,e[i+9],12,-1958414417),a=Y(a,o,s,r,e[i+10],17,-42063),r=Y(r,a,o,s,e[i+11],22,-1990404162),s=Y(s,r,a,o,e[i+12],7,1804603682),o=Y(o,s,r,a,e[i+13],12,-40341101),a=Y(a,o,s,r,e[i+14],17,-1502002290),r=Y(r,a,o,s,e[i+15],22,1236535329),s=j(s,r,a,o,e[i+1],5,-165796510),o=j(o,s,r,a,e[i+6],9,-1069501632),a=j(a,o,s,r,e[i+11],14,643717713),r=j(r,a,o,s,e[i],20,-373897302),s=j(s,r,a,o,e[i+5],5,-701558691),o=j(o,s,r,a,e[i+10],9,38016083),a=j(a,o,s,r,e[i+15],14,-660478335),r=j(r,a,o,s,e[i+4],20,-405537848),s=j(s,r,a,o,e[i+9],5,568446438),o=j(o,s,r,a,e[i+14],9,-1019803690),a=j(a,o,s,r,e[i+3],14,-187363961),r=j(r,a,o,s,e[i+8],20,1163531501),s=j(s,r,a,o,e[i+13],5,-1444681467),o=j(o,s,r,a,e[i+2],9,-51403784),a=j(a,o,s,r,e[i+7],14,1735328473),r=j(r,a,o,s,e[i+12],20,-1926607734),s=W(s,r,a,o,e[i+5],4,-378558),o=W(o,s,r,a,e[i+8],11,-2022574463),a=W(a,o,s,r,e[i+11],16,1839030562),r=W(r,a,o,s,e[i+14],23,-35309556),s=W(s,r,a,o,e[i+1],4,-1530992060),o=W(o,s,r,a,e[i+4],11,1272893353),a=W(a,o,s,r,e[i+7],16,-155497632),r=W(r,a,o,s,e[i+10],23,-1094730640),s=W(s,r,a,o,e[i+13],4,681279174),o=W(o,s,r,a,e[i],11,-358537222),a=W(a,o,s,r,e[i+3],16,-722521979),r=W(r,a,o,s,e[i+6],23,76029189),s=W(s,r,a,o,e[i+9],4,-640364487),o=W(o,s,r,a,e[i+12],11,-421815835),a=W(a,o,s,r,e[i+15],16,530742520),r=W(r,a,o,s,e[i+2],23,-995338651),s=K(s,r,a,o,e[i],6,-198630844),o=K(o,s,r,a,e[i+7],10,1126891415),a=K(a,o,s,r,e[i+14],15,-1416354905),r=K(r,a,o,s,e[i+5],21,-57434055),s=K(s,r,a,o,e[i+12],6,1700485571),o=K(o,s,r,a,e[i+3],10,-1894986606),a=K(a,o,s,r,e[i+10],15,-1051523),r=K(r,a,o,s,e[i+1],21,-2054922799),s=K(s,r,a,o,e[i+8],6,1873313359),o=K(o,s,r,a,e[i+15],10,-30611744),a=K(a,o,s,r,e[i+6],15,-1560198380),r=K(r,a,o,s,e[i+13],21,1309151649),s=K(s,r,a,o,e[i+4],6,-145523070),o=K(o,s,r,a,e[i+11],10,-1120210379),a=K(a,o,s,r,e[i+2],15,718787259),r=K(r,a,o,s,e[i+9],21,-343485551),s=Ue(s,l),r=Ue(r,u),a=Ue(a,d),o=Ue(o,m)}return[s,r,a,o]}(function(e){if(e.length===0)return[];const n=8*e.length,s=new Uint32Array(Or(n));for(let r=0;r<n;r+=8)s[r>>5]|=(255&e[r/8])<<r%32;return s}(t),8*t.length))};cn.default=Ql,Object.defineProperty(un,"__esModule",{value:!0}),un.default=void 0;var eu=io(Fe),tu=io(cn);function io(t){return t&&t.__esModule?t:{default:t}}var nu=(0,eu.default)("v3",48,tu.default);un.default=nu;var dn={},mn={};Object.defineProperty(mn,"__esModule",{value:!0}),mn.default=void 0;var su={randomUUID:typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};mn.default=su,Object.defineProperty(dn,"__esModule",{value:!0}),dn.default=void 0;var Dr=lo(mn),ru=lo(on),au=$e;function lo(t){return t&&t.__esModule?t:{default:t}}var ou=function(t,e,n){if(Dr.default.randomUUID&&!e&&!t)return Dr.default.randomUUID();const s=(t=t||{}).random||(t.rng||ru.default)();if(s[6]=15&s[6]|64,s[8]=63&s[8]|128,e){n=n||0;for(let r=0;r<16;++r)e[n+r]=s[r];return e}return(0,au.unsafeStringify)(s)};dn.default=ou;var hn={},pn={};function iu(t,e,n,s){switch(t){case 0:return e&n^~e&s;case 1:case 3:return e^n^s;case 2:return e&n^e&s^n&s}}function $n(t,e){return t<<e|t>>>32-e}Object.defineProperty(pn,"__esModule",{value:!0}),pn.default=void 0;var lu=function(t){const e=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520];if(typeof t=="string"){const o=unescape(encodeURIComponent(t));t=[];for(let i=0;i<o.length;++i)t.push(o.charCodeAt(i))}else Array.isArray(t)||(t=Array.prototype.slice.call(t));t.push(128);const s=t.length/4+2,r=Math.ceil(s/16),a=new Array(r);for(let o=0;o<r;++o){const i=new Uint32Array(16);for(let l=0;l<16;++l)i[l]=t[64*o+4*l]<<24|t[64*o+4*l+1]<<16|t[64*o+4*l+2]<<8|t[64*o+4*l+3];a[o]=i}a[r-1][14]=8*(t.length-1)/Math.pow(2,32),a[r-1][14]=Math.floor(a[r-1][14]),a[r-1][15]=8*(t.length-1)&4294967295;for(let o=0;o<r;++o){const i=new Uint32Array(80);for(let h=0;h<16;++h)i[h]=a[o][h];for(let h=16;h<80;++h)i[h]=$n(i[h-3]^i[h-8]^i[h-14]^i[h-16],1);let l=n[0],u=n[1],d=n[2],m=n[3],p=n[4];for(let h=0;h<80;++h){const f=Math.floor(h/20),_=$n(l,5)+iu(f,u,d,m)+p+e[f]+i[h]>>>0;p=m,m=d,d=$n(u,30)>>>0,u=l,l=_}n[0]=n[0]+l>>>0,n[1]=n[1]+u>>>0,n[2]=n[2]+d>>>0,n[3]=n[3]+m>>>0,n[4]=n[4]+p>>>0}return[n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,255&n[0],n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,255&n[1],n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,255&n[2],n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,255&n[3],n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,255&n[4]]};pn.default=lu,Object.defineProperty(hn,"__esModule",{value:!0}),hn.default=void 0;var uu=uo(Fe),cu=uo(pn);function uo(t){return t&&t.__esModule?t:{default:t}}var du=(0,uu.default)("v5",80,cu.default);hn.default=du;var gn={};Object.defineProperty(gn,"__esModule",{value:!0}),gn.default=void 0;gn.default="00000000-0000-0000-0000-000000000000";var fn={};Object.defineProperty(fn,"__esModule",{value:!0}),fn.default=void 0;var mu=function(t){return t&&t.__esModule?t:{default:t}}(Ke),hu=function(t){if(!(0,mu.default)(t))throw TypeError("Invalid UUID");return parseInt(t.slice(14,15),16)};function ns(t,e){if(!(t&&e&&t.length&&e.length))throw new Error("Bad alphabet");this.srcAlphabet=t,this.dstAlphabet=e}fn.default=hu,function(t){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NIL",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(t,"parse",{enumerable:!0,get:function(){return u.default}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return l.default}}),Object.defineProperty(t,"v1",{enumerable:!0,get:function(){return e.default}}),Object.defineProperty(t,"v3",{enumerable:!0,get:function(){return n.default}}),Object.defineProperty(t,"v4",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(t,"v5",{enumerable:!0,get:function(){return r.default}}),Object.defineProperty(t,"validate",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(t,"version",{enumerable:!0,get:function(){return o.default}});var e=d(an),n=d(un),s=d(dn),r=d(hn),a=d(gn),o=d(fn),i=d(Ke),l=d($e),u=d(Rt);function d(m){return m&&m.__esModule?m:{default:m}}}(so),ns.prototype.convert=function(t){var e,n,s,r={},a=this.srcAlphabet.length,o=this.dstAlphabet.length,i=t.length,l=typeof t=="string"?"":[];if(!this.isValid(t))throw new Error('Number "'+t+'" contains of non-alphabetic digits ('+this.srcAlphabet+")");if(this.srcAlphabet===this.dstAlphabet)return t;for(e=0;e<i;e++)r[e]=this.srcAlphabet.indexOf(t[e]);do{for(n=0,s=0,e=0;e<i;e++)(n=n*a+r[e])>=o?(r[s++]=parseInt(n/o,10),n%=o):s>0&&(r[s++]=0);i=s,l=this.dstAlphabet.slice(n,n+1).concat(l)}while(s!==0);return l},ns.prototype.isValid=function(t){for(var e=0;e<t.length;++e)if(this.srcAlphabet.indexOf(t[e])===-1)return!1;return!0};var pu=ns;function ft(t,e){var n=new pu(t,e);return function(s){return n.convert(s)}}ft.BIN="01",ft.OCT="01234567",ft.DEC="0123456789",ft.HEX="0123456789abcdef";var gu=ft;const{v4:qn,validate:fu}=so,Lt=gu,Hn={cookieBase90:"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!#$%&'()*+-./:<=>?@[]^_`{|}~",flickrBase58:"123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ",uuid25Base36:"0123456789abcdefghijklmnopqrstuvwxyz"},yu={consistentLength:!0};let Gn;const Fr=(t,e,n)=>{const s=e(t.toLowerCase().replace(/-/g,""));return n&&n.consistentLength?s.padStart(n.shortIdLength,n.paddingChar):s},Ur=(t,e)=>{const n=e(t).padStart(32,"0").match(/(\w{8})(\w{4})(\w{4})(\w{4})(\w{12})/);return[n[1],n[2],n[3],n[4],n[5]].join("-")};var _u=(()=>{const t=(e,n)=>{const s=e||Hn.flickrBase58,r={...yu,...n};if([...new Set(Array.from(s))].length!==s.length)throw new Error("The provided Alphabet has duplicate characters resulting in unreliable results");const a=(o=s.length,Math.ceil(Math.log(2**128)/Math.log(o)));var o;const i={shortIdLength:a,consistentLength:r.consistentLength,paddingChar:s[0]},l=Lt(Lt.HEX,s),u=Lt(s,Lt.HEX),d=()=>Fr(qn(),l,i),m={alphabet:s,fromUUID:p=>Fr(p,l,i),maxLength:a,generate:d,new:d,toUUID:p=>Ur(p,u),uuid:qn,validate:(p,h=!1)=>{if(!p||typeof p!="string")return!1;const f=r.consistentLength?p.length===a:p.length<=a,_=p.split("").every(y=>s.includes(y));return h===!1?f&&_:f&&_&&fu(Ur(p,u))}};return Object.freeze(m),m};return t.constants=Hn,t.uuid=qn,t.generate=()=>(Gn||(Gn=t(Hn.flickrBase58).generate),Gn()),t})();const bu=ea(_u),co={[oe.NOT_STARTED]:"[ ]",[oe.IN_PROGRESS]:"[/]",[oe.COMPLETE]:"[x]",[oe.CANCELLED]:"[-]"},mo=bu(void 0,{consistentLength:!0});function vu(t,e){if(t.uuid===e)return t;if(t.subTasksData)for(const n of t.subTasksData){const s=vu(n,e);if(s)return s}}function ho(t,e={}){const{shallow:n=!1,excludeUuid:s=!1,shortUuid:r=!0}=e;return po(t,{shallow:n,excludeUuid:s,shortUuid:r}).join(`
`)}function po(t,e={}){const{shallow:n=!1,excludeUuid:s=!1,shortUuid:r=!0}=e;let a="";s||(a=`UUID:${r?function(i){try{return mo.fromUUID(i)}catch{return i}}(t.uuid):t.uuid} `);const o=`${co[t.state]} ${a}NAME:${t.name} DESCRIPTION:${t.description}`;return n||!t.subTasksData||t.subTasksData.length===0?[o]:[o,...(t.subTasksData||[]).map(i=>po(i,e).map(l=>`-${l}`)).flat()]}function Eu(t,e){var s;const n=(s=t.subTasksData)==null?void 0:s.map(r=>Eu(r,e));return{...t,uuid:e!=null&&e.keepUuid?t.uuid:crypto.randomUUID(),subTasks:(n==null?void 0:n.map(r=>r.uuid))||[],subTasksData:n}}function td(t,e={}){if(!t.trim())throw new Error("Empty markdown");const n=t.split(`
`);let s=0;for(const u of n)if(u.trim()&&Pr(u)===0)try{ss(u,e),s++}catch{}if(s===0)throw new Error("No root task found");if(s>1)throw new Error(`Multiple root tasks found (${s}). There can only be one root task per conversation. All other tasks must be subtasks (indented with dashes). Root task format: [ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (no dashes). Subtask format: -[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (with dashes).`);const r=t.split(`
`);function a(){for(;r.length>0;){const u=r.shift(),d=Pr(u);try{return{task:ss(u,e),level:d}}catch{}}}const o=a();if(!o)throw new Error("No root task found");const i=[o.task];let l;for(;l=a();){const u=i[l.level-1];if(!u)throw new Error(`Invalid markdown: level ${l.level+1} has no parent
Line: ${l.task.name} is missing a parent
Current tasks: 
${ho(o.task)}`);u.subTasksData&&u.subTasks||(u.subTasks=[],u.subTasksData=[]),u.subTasksData.push(l.task),u.subTasks.push(l.task.uuid),i[l.level]=l.task,i.splice(l.level+1)}return o.task}function Pr(t){let e=0,n=0;for(;n<t.length&&(t[n]===" "||t[n]==="	");)t[n]===" "?e+=.5:t[n]==="	"&&(e+=1),n++;for(;n<t.length&&t[n]==="-";)e+=1,n++;return Math.floor(e)}function ss(t,e={}){const{excludeUuid:n=!1,shortUuid:s=!0}=e;let r=0;for(;r<t.length&&(t[r]===" "||t[r]==="	"||t[r]==="-");)r++;const a=t.substring(r),o=a.match(/^\s*\[([ x\-/?])\]/);if(!o)throw new Error(`Invalid task line: ${t} (missing state)`);const i=o[1],l=Object.entries(co).reduce((h,[f,_])=>(h[_.substring(1,2)]=f,h),{})[i]||oe.NOT_STARTED,u=a.substring(o.index+o[0].length).trim();let d,m,p;if(n){const h=/(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i,f=u.match(h);if(!f){const _=/\b(?:name|NAME):/i.test(u),y=/\b(?:description|DESCRIPTION):/i.test(u);throw!_||!y?new Error(`Invalid task line: ${t} (missing required fields)`):u.toLowerCase().indexOf("name:")<u.toLowerCase().indexOf("description:")?new Error(`Invalid task line: ${t} (invalid format)`):new Error(`Invalid task line: ${t} (incorrect field order)`)}if(m=f[1].trim(),p=f[2].trim(),!m)throw new Error(`Invalid task line: ${t} (missing required fields)`);d=crypto.randomUUID()}else{const h=/(?:uuid|UUID):([^]*?)(?=(?:name|NAME):)(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i,f=u.match(h);if(!f){const _=/\b(?:uuid|UUID):/i.test(u),y=/\b(?:name|NAME):/i.test(u),v=/\b(?:description|DESCRIPTION):/i.test(u);if(!_||!y||!v)throw new Error(`Invalid task line: ${t} (missing required fields)`);const E=u.toLowerCase().indexOf("uuid:"),T=u.toLowerCase().indexOf("name:"),k=u.toLowerCase().indexOf("description:");throw E<T&&T<k?new Error(`Invalid task line: ${t} (invalid format)`):new Error(`Invalid task line: ${t} (incorrect field order)`)}if(d=f[1].trim(),m=f[2].trim(),p=f[3].trim(),!d||!m)throw new Error(`Invalid task line: ${t} (missing required fields)`);if(d==="NEW_UUID")d=crypto.randomUUID();else if(s)try{d=function(_){try{return mo.toUUID(_)}catch{return _}}(d)}catch{}}return{uuid:d,name:m,description:p,state:l,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:Cs.USER}}const ct=t=>({uuid:crypto.randomUUID(),name:"New Task",description:"New task description",state:oe.NOT_STARTED,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:Cs.USER,...t}),Lr=ct({name:"Task 1.1",description:"This is the first sub task",state:oe.IN_PROGRESS}),$r=ct({name:"Task 1.2.1",description:"This is a nested sub task, child of Task 1.2",state:oe.NOT_STARTED}),qr=ct({name:"Task 1.2.2",description:"This is another nested sub task, child of Task 1.2",state:oe.IN_PROGRESS}),Hr=ct({name:"Task 1.2",description:"This is the second sub task",state:oe.COMPLETE,subTasks:[$r.uuid,qr.uuid],subTasksData:[$r,qr]}),Gr=ct({name:"Task 1.3",description:"This is the third sub task",state:oe.CANCELLED}),nd=ho(ct({name:"Task 1",description:"This is the first task",state:oe.NOT_STARTED,subTasks:[Lr.uuid,Hr.uuid,Gr.uuid],subTasksData:[Lr,Hr,Gr]}));function go(t){const e=t.split(`
`);let n=null;const s={created:[],updated:[],deleted:[]};for(const r of e){const a=r.trim();if(a!=="## Created Tasks")if(a!=="## Updated Tasks")if(a!=="## Deleted Tasks"){if(n&&(a.startsWith("[ ]")||a.startsWith("[/]")||a.startsWith("[x]")||a.startsWith("[-]")))try{const o=ss(a,{excludeUuid:!1,shortUuid:!0});o&&s[n].push(o)}catch{}}else n="deleted";else n="updated";else n="created"}return s}function sd(t){const e=t.match(/Created: (\d+), Updated: (\d+), Deleted: (\d+)/);if(e)return{created:parseInt(e[1],10),updated:parseInt(e[2],10),deleted:parseInt(e[3],10)};const n=go(fo(t));return{created:n.created.length,updated:n.updated.length,deleted:n.deleted.length}}function fo(t){const e=t.indexOf("# Task Changes");if(e===-1)return"";const n=t.substring(e),s=[`
New and Updated Tasks:`,`
Remember:`,`

---`];let r=n.length;for(const i of s){const l=n.indexOf(i);l!==-1&&l<r&&(r=l)}const a=n.substring(0,r),o=a.indexOf(`
`);return o===-1?"":a.substring(o+1).trim()}function rd(t){return go(fo(t))}class Tu{static getTaskOrchestratorPrompt(e){const{taskTree:n,surroundingContext:s}=e,r=this.buildTaskContext(n,s);return`Please utilize sub-agents to complete the following task tree.
Here are the details, along with a suggestion prompt.
You may use 1 or more sub-agents in to complete the below task.
For each sub-agent, please give it the relevant context and breakdown of the below task.

## Task Details
**Name:** ${n.name}
${n.description?`**Description:** ${n.description}`:""}
**Status:** ${n.state}

## Task Context
${r}

## Instructions
Please complete this task according to the requirements.
When you are done, report back on the completion status with a summary of changes made,
important context, and other relevant information for the supervisor.

Focus on this specific task tree while being aware of the broader context provided above.`}static getTaskMentionId(e){return`task:${e.taskUuid}:${e.taskTree.name.replace(/\s+/g,"_")}`}static getTaskMentionLabel(e){const{taskTree:n,surroundingContext:s}=e;return s.targetTaskPath.length>1?`${s.targetTaskPath.slice(0,-1).join(" → ")} → ${n.name}`:n.name}static buildTaskContext(e,n){const{rootTask:s,targetTaskPath:r}=n;let a=`This task is part of a larger project: "${s.name}"`;return s.description&&(a+=`

**Project Description:** ${s.description}`),r.length>1&&(a+=`

**Task Path:** ${r.join(" → ")}`),e.subTasksData&&e.subTasksData.length>0&&(a+=`

**Subtasks:**`,e.subTasksData.forEach((o,i)=>{a+=`
${i+1}. ${o.name} (${o.state})`,o.description&&(a+=` - ${o.description}`)})),a}}function mt(t){var e;return((e=t.extraData)==null?void 0:e.isAgentConversation)===!0}var Su=(t=>(t[t.active=0]="active",t[t.inactive=1]="inactive",t))(Su||{});class Iu{constructor(){this._controllers=new Set,this._timeoutIds=new Set}addCallback(e,n){const s=new AbortController,r=setTimeout(()=>{e(s.signal),this._controllers.delete(s),this._timeoutIds.delete(r)},n);this._controllers.add(s),this._timeoutIds.add(r)}cancelAll(){this._controllers.forEach(e=>e.abort()),this._timeoutIds.forEach(e=>clearTimeout(e)),this._controllers.clear(),this._timeoutIds.clear()}}function Bn(t){return t.reduce((e,n)=>e+yo(n),0)}function yo(t){let e=0;return t.request_nodes?e+=JSON.stringify(t.request_nodes).length:e+=(t.request_message||"").length,t.response_nodes?e+=JSON.stringify(t.response_nodes).length:e+=(t.response_text||"").length,e}const me={triggerOnHistorySizeChars:0,historyTailSizeCharsToExclude:0,triggerOnHistorySizeCharsWhenCacheExpiring:0,prompt:"",cacheTTLMs:0,bufferTimeBeforeCacheExpirationMs:0,summaryNodeRequestMessageTemplate:`
<supervisor>
Conversation history between Agent(you) and the user and history of tool calls was summarized to reduce context size.
Summary was generated by Agent(you) so 'I' in the summary represents Agent(you).
Here is the summary:
<summary>
{summary}
</summary>
Continue the conversation and finish the task given by the user from this point.
</supervisor>`,summaryNodeResponseMessage:"Ok. I will continue the conversation from this point."};class wu{constructor(e,n,s){c(this,"historySummaryVersion",2);c(this,"_callbacksManager",new Iu);c(this,"_params");this._conversationModel=e,this._extensionClient=n,this._chatFlagModel=s,this._params=Br(s.historySummaryParams),s.subscribe(r=>{this._params=Br(r.historySummaryParams)})}cancelRunningOrScheduledSummarizations(){this._callbacksManager.cancelAll()}clearStaleHistorySummaryNodes(e){return e.filter(n=>!nt(n)||n.summaryVersion===this.historySummaryVersion)}maybeScheduleSummarization(e){if(!this._chatFlagModel.useHistorySummary||this._params.triggerOnHistorySizeCharsWhenCacheExpiring<=0)return;const n=this._params.cacheTTLMs-e-this._params.bufferTimeBeforeCacheExpirationMs;n>0&&this._callbacksManager.addCallback(s=>{this.maybeAddHistorySummaryNode(!0,s)},n)}preprocessChatHistory(e){const n=e.findLastIndex(s=>nt(s)&&s.summaryVersion===this.historySummaryVersion);return this._chatFlagModel.useHistorySummary?(n>0&&(console.info(`Using history summary node found at index ${n} with requestId: ${e[n].request_id}`),e=e.slice(n)),e=e.filter(s=>!nt(s)||s.summaryVersion===this.historySummaryVersion)):e=e.filter(s=>!nt(s)),e}async maybeAddHistorySummaryNode(e=!1,n){var A,te,ne;if(console.log("maybeAddHistorySummaryNode. isCacheAboutToExpire: ",e),!this._params.prompt||this._params.prompt.trim()==="")return console.log("maybeAddHistorySummaryNode. empty prompt"),!1;const s=this._conversationModel.convertHistoryToExchanges(this._conversationModel.chatHistory),r=e?this._params.triggerOnHistorySizeCharsWhenCacheExpiring:this._params.triggerOnHistorySizeChars;if(console.log("maybeAddHistorySummaryNode. maxCharsThreshold: ",r),r<=0)return!1;const{head:a,tail:o,headSizeChars:i,tailSizeChars:l}=function(S,Ne,ze,R){if(S.length===0)return{head:[],tail:[],headSizeChars:0,tailSizeChars:0};const ce=[],P=[];let de=0,Re=0,xt=0;for(let vn=S.length-1;vn>=0;vn--){const En=S[vn],At=yo(En);de+At<Ne||P.length<R?(P.push(En),xt+=At):(ce.push(En),Re+=At),de+=At}return de<ze?(P.push(...ce),{head:[],tail:P.reverse(),headSizeChars:0,tailSizeChars:de}):{head:ce.reverse(),tail:P.reverse(),headSizeChars:Re,tailSizeChars:xt}}(s,this._params.historyTailSizeCharsToExclude,r,1);if(console.log("maybeAddHistorySummaryNode. headSizeChars: ",i," tailSizeChars: ",l),a.length===0)return console.log("maybeAddHistorySummaryNode. head is empty. nothing to summarize"),!1;const u=Bn(s),d=Bn(a),m=Bn(o),p={totalHistoryCharCount:u,totalHistoryExchangeCount:s.length,headCharCount:d,headExchangeCount:a.length,headLastRequestId:((A=a.at(-1))==null?void 0:A.request_id)??"",tailCharCount:m,tailExchangeCount:o.length,tailLastRequestId:((te=o.at(-1))==null?void 0:te.request_id)??"",summaryCharCount:0,summarizationDurationMs:0,isCacheAboutToExpire:e,isAborted:!1};let h=((ne=a.at(-1))==null?void 0:ne.response_nodes)??[],f=h.filter(S=>S.type===C.TOOL_USE);f.length>0&&(a.at(-1).response_nodes=h.filter(S=>S.type!==C.TOOL_USE)),console.info("Summarizing %d turns of conversation history.",a.length);const _=Date.now(),{responseText:y,requestId:v}=await this._conversationModel.sendSilentExchange({request_message:this._params.prompt,disableRetrieval:!0,disableSelectedCodeDetails:!0,chatHistory:a}),E=Date.now();if(p.summaryCharCount=y.length,p.summarizationDurationMs=E-_,p.isAborted=!!(n!=null&&n.aborted),this._extensionClient.reportAgentRequestEvent({eventName:sn.chatHistorySummarization,conversationId:this._conversationModel.conversationId,requestId:v??"UNKNOWN_REQUEST_ID",chatHistoryLength:this._conversationModel.chatHistory.length,eventData:{chatHistorySummarizationData:p}}),n==null?void 0:n.aborted)return console.log("maybeAddHistorySummaryNode. aborted"),!1;if(!v||y.trim()==="")return console.log("maybeAddHistorySummaryNode. no request id or empty response"),!1;const T=this._params.summaryNodeRequestMessageTemplate.replace("{summary}",y),k=this._params.summaryNodeResponseMessage,w={chatItemType:tt.historySummary,summaryVersion:this.historySummaryVersion,request_id:v,request_message:T,response_text:k,structured_output_nodes:[{id:f.map(S=>S.id).reduce((S,Ne)=>Math.max(S,Ne),-1)+1,type:C.RAW_RESPONSE,content:k},...f],status:I.success,seen_state:z.seen,timestamp:new Date().toISOString()},x=this._conversationModel.chatHistory.findLastIndex(S=>S.request_id===a.at(-1).request_id)+1;return console.info("Adding a history summary node at index %d",x),this._conversationModel.insertChatItem(x,w),!0}}function Br(t){try{if(!t)return console.log("historySummaryParams is empty. Using default params"),me;const e=JSON.parse(t),n={triggerOnHistorySizeChars:e.trigger_on_history_size_chars||me.triggerOnHistorySizeChars,historyTailSizeCharsToExclude:e.history_tail_size_chars_to_exclude||me.historyTailSizeCharsToExclude,triggerOnHistorySizeCharsWhenCacheExpiring:e.trigger_on_history_size_chars_when_cache_expiring||me.triggerOnHistorySizeCharsWhenCacheExpiring,prompt:e.prompt||me.prompt,cacheTTLMs:e.cache_ttl_ms||me.cacheTTLMs,bufferTimeBeforeCacheExpirationMs:e.buffer_time_before_cache_expiration_ms||me.bufferTimeBeforeCacheExpirationMs,summaryNodeRequestMessageTemplate:e.summary_node_request_message_template||me.summaryNodeRequestMessageTemplate,summaryNodeResponseMessage:e.summary_node_response_message||me.summaryNodeResponseMessage};n.summaryNodeRequestMessageTemplate.includes("{summary}")||(console.error("summaryNodeRequestMessageTemplate must contain {summary}. Using default template"),n.summaryNodeRequestMessageTemplate=me.summaryNodeRequestMessageTemplate);const s={...n,prompt:n.prompt.slice(0,10)+"..."};return console.log("historySummaryParams updated: ",s),n}catch(e){return console.error("Failed to parse history_summary_params:",e),me}}const Nu=new Error("request for lock canceled");var Ru=function(t,e,n,s){return new(n||(n=Promise))(function(r,a){function o(u){try{l(s.next(u))}catch(d){a(d)}}function i(u){try{l(s.throw(u))}catch(d){a(d)}}function l(u){var d;u.done?r(u.value):(d=u.value,d instanceof n?d:new n(function(m){m(d)})).then(o,i)}l((s=s.apply(t,e||[])).next())})};class Cu{constructor(e,n=Nu){this._value=e,this._cancelError=n,this._queue=[],this._weightedWaiters=[]}acquire(e=1,n=0){if(e<=0)throw new Error(`invalid weight ${e}: must be positive`);return new Promise((s,r)=>{const a={resolve:s,reject:r,weight:e,priority:n},o=Vr(this._queue,i=>n<=i.priority);o===-1&&e<=this._value?this._dispatchItem(a):this._queue.splice(o+1,0,a)})}runExclusive(e){return Ru(this,arguments,void 0,function*(n,s=1,r=0){const[a,o]=yield this.acquire(s,r);try{return yield n(a)}finally{o()}})}waitForUnlock(e=1,n=0){if(e<=0)throw new Error(`invalid weight ${e}: must be positive`);return this._couldLockImmediately(e,n)?Promise.resolve():new Promise(s=>{this._weightedWaiters[e-1]||(this._weightedWaiters[e-1]=[]),function(r,a){const o=Vr(r,i=>a.priority<=i.priority);r.splice(o+1,0,a)}(this._weightedWaiters[e-1],{resolve:s,priority:n})})}isLocked(){return this._value<=0}getValue(){return this._value}setValue(e){this._value=e,this._dispatchQueue()}release(e=1){if(e<=0)throw new Error(`invalid weight ${e}: must be positive`);this._value+=e,this._dispatchQueue()}cancel(){this._queue.forEach(e=>e.reject(this._cancelError)),this._queue=[]}_dispatchQueue(){for(this._drainUnlockWaiters();this._queue.length>0&&this._queue[0].weight<=this._value;)this._dispatchItem(this._queue.shift()),this._drainUnlockWaiters()}_dispatchItem(e){const n=this._value;this._value-=e.weight,e.resolve([n,this._newReleaser(e.weight)])}_newReleaser(e){let n=!1;return()=>{n||(n=!0,this.release(e))}}_drainUnlockWaiters(){if(this._queue.length===0)for(let e=this._value;e>0;e--){const n=this._weightedWaiters[e-1];n&&(n.forEach(s=>s.resolve()),this._weightedWaiters[e-1]=[])}else{const e=this._queue[0].priority;for(let n=this._value;n>0;n--){const s=this._weightedWaiters[n-1];if(!s)continue;const r=s.findIndex(a=>a.priority<=e);(r===-1?s:s.splice(0,r)).forEach(a=>a.resolve())}}}_couldLockImmediately(e,n){return(this._queue.length===0||this._queue[0].priority<n)&&e<=this._value}}function Vr(t,e){for(let n=t.length-1;n>=0;n--)if(e(t[n]))return n;return-1}var ku=function(t,e,n,s){return new(n||(n=Promise))(function(r,a){function o(u){try{l(s.next(u))}catch(d){a(d)}}function i(u){try{l(s.throw(u))}catch(d){a(d)}}function l(u){var d;u.done?r(u.value):(d=u.value,d instanceof n?d:new n(function(m){m(d)})).then(o,i)}l((s=s.apply(t,e||[])).next())})};class xu{constructor(e){this._semaphore=new Cu(1,e)}acquire(){return ku(this,arguments,void 0,function*(e=0){const[,n]=yield this._semaphore.acquire(1,e);return n})}runExclusive(e,n=0){return this._semaphore.runExclusive(()=>e(),1,n)}isLocked(){return this._semaphore.isLocked()}waitForUnlock(e=0){return this._semaphore.waitForUnlock(1,e)}release(){this._semaphore.isLocked()&&this._semaphore.release()}cancel(){return this._semaphore.cancel()}}function _o(t){throw new Error("Logger not initialized. Call setLibraryLogger() before using getLogger().")}class $t{constructor(){this._disposables=[]}add(e){if(e===void 0)throw new Error("Attempt to add undefined disposable to DisposableCollection");return this._disposables.push(e),e}addAll(...e){e.forEach(n=>this.add(n))}adopt(e){this._disposables.push(...e._disposables),e._disposables.length=0}dispose(){for(const e of this._disposables)e.dispose();this._disposables.length=0}}class Au{constructor(e=new $t,n=new $t){this._disposables=new $t,this._priorityDisposables=new $t,this._disposables.adopt(e),this._priorityDisposables.adopt(n)}addDisposable(e,n=!1){return n?this._priorityDisposables.add(e):this._disposables.add(e)}addDisposables(...e){this._disposables.addAll(...e)}dispose(){this._priorityDisposables.dispose(),this._disposables.dispose()}}var be=(t=>(t[t.Unknown=0]="Unknown",t[t.File=1]="File",t[t.Directory=2]="Directory",t[t.SymbolicLink=64]="SymbolicLink",t))(be||{});const As=class As{static setClientWorkspaces(e){this._instance===void 0?this._instance=e:_o().warn("Attempting to initialize client workspaces when one is already configured. Keeping existing client workspaces.")}static getClientWorkspaces(){if(this._instance===void 0)throw new Error("ClientWorkspaces not set");return this._instance}static reset(){this._instance=void 0}};As._instance=void 0;let rs=As;const Ae=()=>rs.getClientWorkspaces(),Mu=[".md",".mdc"],Yr=[{directory:".cursor/rules",file:".cursorrules",name:"Cursor"},{directory:".windsurf/rules",file:".windsurfrules",name:"Windsurf"},{directory:".github/instructions",file:".github/copilot-instructions.md",name:"GitHub Copilot"},{directory:".clinerules",file:".clinerules",name:"Cline"},{directory:".roo/rules",file:".roorules",name:"Roo Code"},{directory:".trae/rules",name:"Trae"}];class ks extends Au{constructor(){super(),this._logger=_o()}async loadRules({includeGuidelines:e=!1,query:n,maxResults:s,contextRules:r}={}){this._logger.debug(`Loading rules with includeGuidelines=${e}, query=${n}, maxResults=${s}`),this._logger.debug("Using file system approach to load rules");const a=await this.loadDirectory((void 0)(Qe,gt));let o;if(this._logger.debug(`Loaded ${a.length} rules from directory`),e){const i=await this.loadGuidelinesFiles();this._logger.debug(`Loaded ${i.length} guidelines rules`),o=[...i,...a]}else o=a;if(n&&n.trim()){const i=n.toLowerCase().trim();o=o.filter(l=>{const u=l.path.toLowerCase().includes(i),d=l.content.toLowerCase().includes(i);return u||d}),this._logger.debug(`Filtered to ${o.length} rules matching query: ${n}`)}return s&&s>0&&(o=o.slice(0,s),this._logger.debug(`Limited to ${o.length} rules`)),this._logger.debug(`Returning ${o.length} total rules`),r!==void 0&&(o=ks.filterRulesByContext(o,r),this._logger.debug(`Filtered to ${o.length} rules based on context`)),o}static filterRulesByContext(e,n){return[...e.filter(s=>s.type!==le.MANUAL),...e.filter(s=>s.type===le.MANUAL&&n.some(r=>r.path===s.path))]}async loadGuidelinesFiles(){const e=[],n=Ae();if(!n)return this._logger.warn("Client workspaces not initialized"),e;const s=await n.getWorkspaceRoot();if(!s)return e;const r=(void 0)(s,Dn),a=await n.getPathInfo(r);if(a.exists&&a.type===be.File)try{const o=(await n.readFile(r)).contents;if(!o)return this._logger.warn(`Guidelines file is empty: ${r}`),e;const i=Ye.parseRuleFile(o,Dn);e.push({path:Dn,content:i.content,type:i.type,description:i.description})}catch(o){this._logger.error(`Error loading guidelines file ${r}: ${String(o)}`)}return e}async loadDirectory(e){const n=[];try{const s=Ae();if(!s)return this._logger.warn("Client workspaces not initialized"),n;const r=await s.getWorkspaceRoot();if(!r)return this._logger.warn("No workspace root found"),n;const a=(void 0)(r,e);this._logger.debug(`Looking for rules in: ${a}`);const o=await s.getPathInfo(a);return this._logger.debug(`Path info for ${a}: ${JSON.stringify(o)}`),o.exists&&o.type===be.Directory?(this._logger.debug(`Rules folder exists at ${a}`),await this.processRuleDirectory(s,a,n,""),this._logger.debug(`Loaded ${n.length} rules from ${a}`),n):(this._logger.debug(`Rules folder not found at ${a}`),n)}catch(s){return this._logger.error(`Error loading rules: ${String(s)}`),n}}async loadDirectoryFromPath(e){const n=[];try{const s=Ae();if(!s)return this._logger.warn("Client workspaces not initialized"),n;let r;if(!(void 0)(e)){const o=await s.getWorkspaceRoot();if(!o)return this._logger.warn("No workspace root found"),n;r=(void 0)(o,e),this._logger.debug(`Loading rules from workspace-relative path: ${r}`)}const a=await s.getPathInfo(r);return a.exists&&a.type===be.Directory?(this._logger.debug(`Rules folder exists at ${r}`),await this.processRuleDirectory(s,r,n,""),this._logger.debug(`Loaded ${n.length} rules from ${r}`),n):(this._logger.debug(`Rules folder not found at ${r}`),n)}catch(s){return this._logger.error(`Error loading rules from path: ${String(s)}`),n}}async processRuleDirectory(e,n,s,r){const a=await e.listDirectory(n,1,!1);if(a.errorMessage)this._logger.error(`Error listing directory ${n}: ${a.errorMessage}`);else{this._logger.debug(`Processing directory: ${n}, found ${a.entries.length} entries`);for(const o of a.entries){const i=(void 0)(n,o),l=(void 0)(r,o),u=await e.getPathInfo(i);if(u.exists)if(u.type===be.Directory)this._logger.debug(`Processing subdirectory: ${o}`),await this.processRuleDirectory(e,i,s,l);else if(u.type===be.File&&Mu.some(d=>o.endsWith(d))){this._logger.debug(`Processing rule file: ${o}`);try{const d=(await e.readFile(i)).contents||"",m=Ye.parseRuleFile(d,o);s.push({path:l,content:m.content,type:m.type,description:m.description}),this._logger.debug(`Successfully loaded rule: ${l}`)}catch(d){this._logger.error(`Error loading rule file ${i}: ${String(d)}`)}}else u.type===be.File&&this._logger.debug(`Skipping non-markdown file: ${o}`)}}}async createRule(e,n=!1){const s=Ae();if(!s)throw new Error("Client workspaces not initialized");const r=await s.getWorkspaceRoot();if(!r)throw new Error("No workspace root found");let a=(void 0)(r,Qe,gt);n&&(a=(void 0)(a,"imported"));const o=e.path.endsWith(".md")?e.path:`${e.path}.md`,i=(void 0)(a,o),l=await s.getQualifiedPathName(i);if(!l)throw new Error(`Unable to get qualified path for: ${i}`);if((await s.getPathInfo(i)).exists)throw new Error(`Rule file already exists: ${o}`);const u=Ye.formatRuleFileForMarkdown(e);return await s.writeFile(l,u),{...e,path:o}}async deleteRule(e){if(typeof e!="string")throw new Error(`Expected rulePath to be a string, got ${typeof e}: ${String(e)}`);const n=Ae();if(!n)throw new Error("Client workspaces not initialized");const s=await n.getWorkspaceRoot();if(!s)throw new Error("No workspace root found");let r;if((void 0)(e)||(r=(void 0)(s,Qe,gt,e)),(await n.getPathInfo(r)).exists){const a=await n.getQualifiedPathName(r);a&&(await n.deleteFile(a),this._logger.debug(`Deleted rule file: ${r}`)),this._logger.debug(`Deleted rule file: ${r}`)}}async updateRuleFile(e,n){if(typeof e!="string")throw new Error(`Expected rulePath to be a string, got ${typeof e}: ${String(e)}`);const s=Ae();if(!s)throw new Error("Client workspaces not initialized");const r=await s.getWorkspaceRoot();if(!r)throw new Error("No workspace root found");let a;(void 0)(e)||(a=e.startsWith(Qe)?(void 0)(r,e):(void 0)(r,Qe,gt,e));const o=await s.getQualifiedPathName(a);if(!o)throw new Error(`Unable to get qualified path for: ${a}`);await s.writeFile(o,n),this._logger.debug(`Updated rule file: ${a}`)}async importFile(e,n){const s=Ae();if(!s)throw new Error("Client workspaces not initialized");let r,a;if(!(void 0)(e)){const i=await s.getWorkspaceRoot();if(!i)throw new Error("No workspace root found");r=(void 0)(i,e),a=e,this._logger.debug(`Importing file from workspace-relative path: ${r}`)}const o=await s.getPathInfo(r);if(!o.exists||o.type!==be.File)return this._logger.error(`File not found: ${r}`),{successfulImports:0,duplicates:0,totalAttempted:1};try{const i=(await s.readFile(r)).contents;if(!i)return this._logger.error(`File is empty: ${r}`),{successfulImports:0,duplicates:0,totalAttempted:1};const l=Ye.parseRuleFile(i,a),u=(void 0)(a).name.replace(".","");return await this.createRule({path:u,content:l.content,type:l.type},n),{successfulImports:1,duplicates:0,totalAttempted:1}}catch(i){return this._logger.error(`Error importing file ${e}: ${String(i)}`),{successfulImports:0,duplicates:String(i).includes("already exists")?1:0,totalAttempted:1}}}async importDirectory(e,n){try{const s=await this.loadDirectoryFromPath(e);if(s.length===0)return this._logger.debug(`No rules found in directory: ${e}`),{successfulImports:0,duplicates:0,totalAttempted:0};this._logger.debug(`Loaded ${s.length} existing rules from ${e}`);let r=0,a=0;const o=s.length;for(const i of s)try{const l=(void 0)(i.path).name,u=(void 0)(i.path),d=u==="."?l:(void 0)(u,l);await this.createRule({path:d,content:i.content,type:i.type},n),r++,this._logger.debug(`Successfully imported rule: ${i.path} -> ${d}`)}catch(l){this._logger.warn(`Failed to import rule ${i.path}: ${String(l)}`),String(l).includes("already exists")&&a++}return this._logger.info(`Imported ${r} rules from ${e}, ${a} duplicates skipped`),{successfulImports:r,duplicates:a,totalAttempted:o}}catch(s){return this._logger.error(`Error importing directory: ${String(s)}`),{successfulImports:0,duplicates:0,totalAttempted:0}}}async detectAutoImportOptions(){const e=Ae();if(!e)return this._logger.warn("No workspace available for auto-import detection"),[];const n=await e.getWorkspaceRoot();if(!n)return this._logger.warn("No workspace root found for auto-import detection"),[];const s=[];for(const{directory:r,file:a,name:o}of Yr){let i=!1,l=!1;if(r)try{const u=(void 0)(n,r),d=await e.getPathInfo(u);i=d.exists===!0&&d.type===be.Directory}catch(u){this._logger.debug(`Error checking directory ${r}: ${String(u)}`)}if(a)try{const u=(void 0)(n,a),d=await e.getPathInfo(u);l=d.exists===!0&&d.type===be.File}catch(u){this._logger.debug(`Error checking file ${a}: ${String(u)}`)}i&&l?s.push({label:o,description:`Import existing rules from ${r} and ${a}`,directory:r,file:a}):i?s.push({label:o,description:`Import existing rules from ${r}`,directory:r}):l&&s.push({label:o,description:`Import existing rules from ${a}`,file:a})}return s}async processAutoImportSelection(e){const n=Yr.find(l=>l.name===e);if(!n)throw new Error(`Unknown auto-import option: ${e}`);const s=[];n.directory&&s.push(this.importDirectory(n.directory,!0)),n.file&&s.push(this.importFile(n.file,!0));const r=await Promise.all(s),a=r.reduce((l,u)=>l+u.successfulImports,0),o=r.reduce((l,u)=>l+u.duplicates,0),i=r.reduce((l,u)=>l+u.totalAttempted,0);return this._logger.debug(`Auto-import rules completed for ${e}, imported: ${a}, duplicates: ${o}, total attempted: ${i}`),{importedRulesCount:a,duplicatesCount:o,totalAttempted:i,source:e}}autoImportRules(){this._logger.debug("Auto import rules requested")}dispose(){super.dispose()}}const Xt="temp-fe";class se{constructor(e,n,s,r,a,o){c(this,"_state");c(this,"_subscribers",new Set);c(this,"_focusModel",new Ji);c(this,"_onSendExchangeListeners",[]);c(this,"_onNewConversationListeners",[]);c(this,"_onHistoryDeleteListeners",[]);c(this,"_onBeforeChangeConversationListeners",[]);c(this,"_totalCharactersCacheThrottleMs",1e3);c(this,"_sendUserMessageMutex",new xu);c(this,"_totalCharactersStore");c(this,"_chatHistorySummarizationModel");c(this,"subscribe",e=>(this._subscribers.add(e),e(this),()=>{this._subscribers.delete(e)}));c(this,"setConversation",(e,n=!0,s=!0)=>{const r=e.id!==this._state.id;r&&s&&(e.toolUseStates=Object.fromEntries(Object.entries(e.toolUseStates??{}).map(([o,i])=>{if(i.requestId&&i.toolUseId){const{requestId:l,toolUseId:u}=xr(o);return l===i.requestId&&u===i.toolUseId||console.warn("Tool use state key does not match request and tool use IDs. Got key ",o,"but object has ",Fn(i)),[o,i]}return[o,{...i,...xr(o)}]})),(e=this._notifyBeforeChangeConversation(this._state,e)).lastInteractedAtIso=new Date().toISOString()),n&&r&&this.isValid&&(this.saveDraftActiveContextIds(),this._unloadContextFromConversation(this._state));const a=se.isEmpty(e);if(r&&a){const o=this._state.draftExchange;o&&(e.draftExchange=o)}return this._state=e,this._focusModel.setItems(this._state.chatHistory.filter(L)),this._focusModel.initFocusIdx(-1),this._subscribers.forEach(o=>o(this)),this._saveConversation(this._state),r&&(this._loadContextFromConversation(e),this.loadDraftActiveContextIds(),this._onNewConversationListeners.forEach(o=>o())),!0});c(this,"update",e=>{this.setConversation({...this._state,...e}),this._totalCharactersStore.updateStore()});c(this,"toggleIsPinned",()=>{this.update({isPinned:!this.isPinned})});c(this,"setName",e=>{this.update({name:e})});c(this,"setSelectedModelId",e=>{this.update({selectedModelId:e})});c(this,"updateFeedback",(e,n)=>{this.update({feedbackStates:{...this._state.feedbackStates,[e]:n}})});c(this,"updateToolUseState",e=>{this.update({toolUseStates:{...this._state.toolUseStates,[Fn(e)]:e}})});c(this,"getToolUseState",(e,n)=>e===void 0||n===void 0||this.toolUseStates===void 0?{phase:M.unknown,requestId:e??"",toolUseId:n??""}:this.toolUseStates[Fn({requestId:e,toolUseId:n})]||{phase:M.new});c(this,"getLastToolUseId",()=>{var s,r;const e=this.lastExchange;if(!e)return;const n=(((s=e==null?void 0:e.structured_output_nodes)==null?void 0:s.filter(a=>a.type===C.TOOL_USE))??[]).at(-1);return n?(r=n.tool_use)==null?void 0:r.tool_use_id:void 0});c(this,"getLastToolUseState",()=>{var s;const e=this.lastExchange;if(!e)return{phase:M.unknown};const n=function(r=[]){let a;for(const o of r){if(o.type===C.TOOL_USE)return o;o.type===C.TOOL_USE_START&&(a=o)}return a}(e==null?void 0:e.structured_output_nodes);return n?this.getToolUseState(e.request_id,(s=n.tool_use)==null?void 0:s.tool_use_id):{phase:M.unknown}});c(this,"addExchange",(e,n)=>{const s=this._state.chatHistory;let r,a;r=n===void 0?[...s,e]:n===-1?s.length===0?[e]:[...s.slice(0,-1),e,s[s.length-1]]:[...s.slice(0,n),e,...s.slice(n)],L(e)&&(a=e.request_id?{...this._state.feedbackStates,[e.request_id]:{selectedRating:to.unset,feedbackNote:""}}:void 0),this.update({chatHistory:r,...a?{feedbackStates:a}:{},lastUrl:void 0})});c(this,"addExchangeBeforeLast",e=>{this.addExchange(e,-1)});c(this,"resetShareUrl",()=>{this.update({lastUrl:void 0})});c(this,"updateExchangeById",(e,n,s=!1)=>{var i;const r=this.exchangeWithRequestId(n);if(r===null)return console.warn("No exchange with this request ID found."),!1;s&&e.response_text!==void 0&&(e.response_text=(r.response_text??"")+(e.response_text??"")),s&&(e.structured_output_nodes=function(l=[]){const u=Fl(l);return u&&u.type===C.TOOL_USE?l.filter(d=>d.type!==C.TOOL_USE_START):l}([...r.structured_output_nodes??[],...e.structured_output_nodes??[]])),e.stop_reason!==r.stop_reason&&r.stop_reason&&e.stop_reason===No.REASON_UNSPECIFIED&&(e.stop_reason=r.stop_reason),s&&e.workspace_file_chunks!==void 0&&(e.workspace_file_chunks=[...r.workspace_file_chunks??[],...e.workspace_file_chunks??[]]);const a=(i=(e.structured_output_nodes||[]).find(l=>l.type===C.MAIN_TEXT_FINISHED))==null?void 0:i.content;a&&a!==e.response_text&&(e.response_text=a);let o=this._state.isShareable||Vn({...r,...e});return this.update({chatHistory:this.chatHistory.map(l=>l.request_id===n?{...l,...e}:l),isShareable:o}),!0});c(this,"clearMessagesFromHistory",e=>{const n=this._collectToolUseIdsFromMessages(this.chatHistory.filter(s=>s.request_id&&e.has(s.request_id)));this.update({chatHistory:this.chatHistory.filter(s=>!s.request_id||!e.has(s.request_id))}),this._extensionClient.clearMetadataFor({requestIds:Array.from(e),toolUseIds:n})});c(this,"clearHistory",()=>{const e=this._collectToolUseIdsFromMessages(this.chatHistory);this._extensionClient.clearMetadataFor({requestIds:this.requestIds,toolUseIds:e}),this.update({chatHistory:[]})});c(this,"clearHistoryFrom",async(e,n=!0)=>{const s=this.historyFrom(e,n),r=s.map(o=>o.request_id).filter(o=>o!==void 0),a=this._collectToolUseIdsFromMessages(s);this.update({chatHistory:this.historyTo(e,!n)}),this._extensionClient.clearMetadataFor({requestIds:r,toolUseIds:a}),s.forEach(o=>{this._onHistoryDeleteListeners.forEach(i=>i(o))})});c(this,"clearMessageFromHistory",e=>{const n=this.chatHistory.find(r=>r.request_id===e),s=n?this._collectToolUseIdsFromMessages([n]):[];this.update({chatHistory:this.chatHistory.filter(r=>r.request_id!==e)}),this._extensionClient.clearMetadataFor({requestIds:[e],toolUseIds:s})});c(this,"_collectToolUseIdsFromMessages",e=>{var s;const n=[];for(const r of e)if(L(r)&&r.structured_output_nodes)for(const a of r.structured_output_nodes)a.type===C.TOOL_USE&&((s=a.tool_use)!=null&&s.tool_use_id)&&n.push(a.tool_use.tool_use_id);return n});c(this,"historyTo",(e,n=!1)=>{const s=this.chatHistory.findIndex(r=>r.request_id===e);return s===-1?[]:this.chatHistory.slice(0,n?s+1:s)});c(this,"historyFrom",(e,n=!0)=>{const s=this.chatHistory.findIndex(r=>r.request_id===e);return s===-1?[]:this.chatHistory.slice(n?s:s+1)});c(this,"resendLastExchange",async()=>{const e=this.lastExchange;if(e&&!this.awaitingReply)return this.resendTurn(e)});c(this,"resendTurn",e=>this.awaitingReply?Promise.resolve():(this._removeTurn(e),this.sendExchange({chatItemType:e.chatItemType,request_message:e.request_message,rich_text_json_repr:e.rich_text_json_repr,status:I.draft,mentioned_items:e.mentioned_items,structured_request_nodes:e.structured_request_nodes,disableSelectedCodeDetails:e.disableSelectedCodeDetails,chatHistory:e.chatHistory,model_id:e.model_id},!1,e.request_id)));c(this,"_removeTurn",e=>{this.update({chatHistory:this.chatHistory.filter(n=>n!==e&&(!e.request_id||n.request_id!==e.request_id))})});c(this,"exchangeWithRequestId",e=>this.chatHistory.find(n=>n.request_id===e)||null);c(this,"resetTotalCharactersCache",()=>{this._totalCharactersStore.resetCache()});c(this,"markSeen",async e=>{if(!e.request_id||!this.chatHistory.find(s=>s.request_id===e.request_id))return;const n={seen_state:z.seen};this.update({chatHistory:this.chatHistory.map(s=>s.request_id===e.request_id?{...s,...n}:s)})});c(this,"createStructuredRequestNodes",e=>this._jsonToStructuredRequest(e));c(this,"saveDraftMentions",e=>{if(!this.draftExchange)return;const n=e.filter(s=>!s.personality&&!s.task);this.update({draftExchange:{...this.draftExchange,mentioned_items:n}})});c(this,"saveDraftActiveContextIds",()=>{const e=this._specialContextInputModel.recentActiveItems.map(n=>n.id);this.update({draftActiveContextIds:e})});c(this,"loadDraftActiveContextIds",()=>{const e=new Set(this.draftActiveContextIds??[]),n=this._specialContextInputModel.recentItems.filter(r=>e.has(r.id)||r.recentFile||r.selection||r.sourceFolder),s=this._specialContextInputModel.recentItems.filter(r=>!(e.has(r.id)||r.recentFile||r.selection||r.sourceFolder));this._specialContextInputModel.markItemsActive(n.reverse()),this._specialContextInputModel.markItemsInactive(s.reverse())});c(this,"saveDraftExchange",(e,n)=>{var o,i,l;const s=e!==((o=this.draftExchange)==null?void 0:o.request_message),r=n!==((i=this.draftExchange)==null?void 0:i.rich_text_json_repr);if(!s&&!r)return;const a=(l=this.draftExchange)==null?void 0:l.mentioned_items;this.update({draftExchange:{request_message:e,rich_text_json_repr:n,mentioned_items:a,status:I.draft}})});c(this,"clearDraftExchange",()=>{const e=this.draftExchange;return this.update({draftExchange:void 0}),e});c(this,"sendDraftExchange",()=>{if(this._extensionClient.triggerUsedChatMetric(),!this.canSendDraft||!this.draftExchange)return!1;const e=this.clearDraftExchange();if(!e)return!1;const n=this._chatFlagModel.enableChatMultimodal&&e.rich_text_json_repr?this._jsonToStructuredRequest(e.rich_text_json_repr):void 0;return this.sendExchange({...e,structured_request_nodes:n,model_id:this.selectedModelId??void 0}).then(()=>{var s;if(!mt(this)){const r=!this.name&&this.chatHistory.length===1&&((s=this.firstExchange)==null?void 0:s.request_id)===this.chatHistory[0].request_id;this._chatFlagModel.summaryTitles&&r&&this.updateConversationTitle()}}).finally(()=>{var s;mt(this)&&this._extensionClient.reportAgentRequestEvent({eventName:sn.sentUserMessage,conversationId:this.id,requestId:((s=this.lastExchange)==null?void 0:s.request_id)??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length})}),this.focusModel.setFocusIdx(void 0),!0});c(this,"cancelMessage",async()=>{var e;this.canCancelMessage&&((e=this.lastExchange)!=null&&e.request_id)&&(this.updateExchangeById({status:I.cancelled},this.lastExchange.request_id),await this._extensionClient.cancelChatStream(this.lastExchange.request_id))});c(this,"sendInstructionExchange",async(e,n)=>{let s=`${Xt}-${crypto.randomUUID()}`;const r={status:I.sent,request_id:s,request_message:e,model_id:this.selectedModelId??void 0,structured_output_nodes:[],seen_state:z.unseen,timestamp:new Date().toISOString()};this.addExchange(r);for await(const a of this._extensionClient.sendInstructionMessage(r,n)){if(!this.updateExchangeById(a,s,!0))return;s=a.request_id||s}});c(this,"updateConversationTitle",async()=>{const{responseText:e}=await this.sendSummaryExchange();this.update({name:e})});c(this,"checkAndGenerateAgentTitle",()=>{var n;if(!(!mt(this)||!this._chatFlagModel.summaryTitles||this.name)){var e;!this.name&&(e=this.chatHistory,e.filter(s=>ts(s))).length===1&&!((n=this.extraData)!=null&&n.hasTitleGenerated)&&(this.update({extraData:{...this.extraData,hasTitleGenerated:!0}}),this.updateConversationTitle())}});c(this,"sendSummaryExchange",()=>{const e={status:I.sent,request_message:"Please provide a clear and concise summary of our conversation so far. The summary must be less than 6 words long. The summary must contain the key points of the conversation. The summary must be in the form of a title which will represent the conversation. The response should not include any additional formatting such as wrapping the response with quotation marks.",model_id:this.selectedModelId??void 0,chatItemType:tt.summaryTitle,disableRetrieval:!0,disableSelectedCodeDetails:!0};return this.sendSilentExchange(e)});c(this,"generateCommitMessage",async()=>{let e=`${Xt}-${crypto.randomUUID()}`;const n={status:I.sent,request_id:e,request_message:"Please generate a commit message based on the diff of my staged and unstaged changes.",model_id:this.selectedModelId??void 0,mentioned_items:[],seen_state:z.unseen,chatItemType:tt.generateCommitMessage,disableSelectedCodeDetails:!0,chatHistory:[],timestamp:new Date().toISOString()};this.addExchange(n);for await(const s of this._extensionClient.generateCommitMessage()){if(!this.updateExchangeById(s,e,!0))return;e=s.request_id||e}});c(this,"sendExchange",async(e,n=!1,s)=>{var d;this._chatHistorySummarizationModel.cancelRunningOrScheduledSummarizations(),this.updateLastInteraction();let r=`${Xt}-${crypto.randomUUID()}`,a=this._chatFlagModel.isModelIdValid(e.model_id)?e.model_id:void 0;if(se.isNew(this._state)){const m=crypto.randomUUID(),p=this._state.id;try{await this._extensionClient.migrateConversationId(p,m)}catch(h){console.error("Failed to migrate conversation checkpoints:",h)}this._state={...this._state,id:m},this._saveConversation(this._state,!0),this._extensionClient.setCurrentConversation(m),this._subscribers.forEach(h=>h(this))}e=Wr(e);let o={status:I.sent,request_id:r,request_message:e.request_message,rich_text_json_repr:e.rich_text_json_repr,model_id:a,mentioned_items:e.mentioned_items,structured_output_nodes:e.structured_output_nodes,seen_state:z.unseen,chatItemType:e.chatItemType,disableSelectedCodeDetails:e.disableSelectedCodeDetails,chatHistory:e.chatHistory,structured_request_nodes:e.structured_request_nodes,timestamp:new Date().toISOString()};this.addExchange(o),this._loadContextFromExchange(o),this._onSendExchangeListeners.forEach(m=>m(o)),this._chatFlagModel.useHistorySummary&&!e.request_message&&await this._chatHistorySummarizationModel.maybeAddHistorySummaryNode()&&this.update({chatHistory:this._chatHistorySummarizationModel.clearStaleHistorySummaryNodes(this.chatHistory)}),o=await this._addIdeStateNode(o),this.updateExchangeById({structured_request_nodes:o.structured_request_nodes},r,!1);const i=Date.now();let l=!1;for await(const m of this.sendUserMessage(r,o,n,s)){if(((d=this.exchangeWithRequestId(r))==null?void 0:d.status)!==I.sent||!this.updateExchangeById(m,r,!0))return;if(r=m.request_id||r,!l&&mt(this)){const p=Date.now(),h=p-i;this._extensionClient.reportAgentRequestEvent({eventName:sn.firstTokenReceived,conversationId:this.id,requestId:r,chatHistoryLength:this.chatHistory.length,eventData:{firstTokenTimingData:{userMessageSentTimestampMs:i,firstTokenReceivedTimestampMs:p,timeToFirstTokenMs:h}}}),l=!0}}const u=Date.now()-i;this._chatHistorySummarizationModel.maybeScheduleSummarization(u)});c(this,"sendSuggestedQuestion",e=>{this.sendExchange({request_message:e,status:I.draft}),this._extensionClient.triggerUsedChatMetric(),this._extensionClient.reportWebviewClientEvent(Ja.chatUseSuggestedQuestion)});c(this,"recoverAllExchanges",async()=>{await Promise.all(this.recoverableExchanges.map(this.recoverExchange))});c(this,"recoverExchange",async e=>{var r;if(!e.request_id||e.status!==I.sent)return;let n=e.request_id;const s=(r=e.structured_output_nodes)==null?void 0:r.filter(a=>a.type===C.AGENT_MEMORY);this.updateExchangeById({...e,response_text:e.lastChunkId?e.response_text:"",structured_output_nodes:e.lastChunkId?e.structured_output_nodes??[]:s},n);for await(const a of this.getChatStream(e)){if(!this.updateExchangeById(a,n,!0))return;n=a.request_id||n}});c(this,"_loadContextFromConversation",e=>{e.chatHistory.forEach(n=>{L(n)&&this._loadContextFromExchange(n)})});c(this,"_loadContextFromExchange",e=>{e.mentioned_items&&(this._specialContextInputModel.updateItems(e.mentioned_items,[]),this._specialContextInputModel.markItemsActive(e.mentioned_items))});c(this,"_unloadContextFromConversation",e=>{e.chatHistory.forEach(n=>{L(n)&&this._unloadContextFromExchange(n)})});c(this,"_unloadContextFromExchange",e=>{e.mentioned_items&&this._specialContextInputModel.updateItems([],e.mentioned_items)});c(this,"updateLastInteraction",()=>{this.update({lastInteractedAtIso:new Date().toISOString()})});c(this,"_jsonToStructuredRequest",e=>{const n=[],s=a=>{var i;const o=n.at(-1);if((o==null?void 0:o.type)===G.TEXT){const l=((i=o.text_node)==null?void 0:i.content)??"",u={...o,text_node:{content:l+a}};n[n.length-1]=u}else n.push({id:n.length,type:G.TEXT,text_node:{content:a}})},r=a=>{var o,i,l,u,d;if(a.type==="doc"||a.type==="paragraph")for(const m of a.content??[])r(m);else if(a.type==="hardBreak")s(`
`);else if(a.type==="text")s(a.text??"");else if(a.type==="file"){if(typeof((o=a.attrs)==null?void 0:o.src)!="string")return void console.error("File source is not a string: ",(i=a.attrs)==null?void 0:i.src);if(a.attrs.isLoading)return;const m=(l=a.attrs)==null?void 0:l.title,p=Co(m);ko(m)?n.push({id:n.length,type:G.IMAGE_ID,image_id_node:{image_id:a.attrs.src,format:p}}):n.push({id:n.length,type:G.FILE_ID,file_id_node:{file_id:a.attrs.src,file_name:m}})}else if(a.type==="mention"){const m=(u=a.attrs)==null?void 0:u.data;m&&no(m)?n.push({id:n.length,type:G.TEXT,text_node:{content:$l(this._chatFlagModel,m.personality.type)}}):m&&Nl(m)?n.push({id:n.length,type:G.TEXT,text_node:{content:Tu.getTaskOrchestratorPrompt(m.task)}}):s(`@\`${(m==null?void 0:m.name)??(m==null?void 0:m.id)}\``)}else if(a.type==="askMode"){const m=(d=a.attrs)==null?void 0:d.prompt;m&&n.push({id:n.length,type:G.TEXT,text_node:{content:m}})}};return r(e),n});this._extensionClient=e,this._chatFlagModel=n,this._specialContextInputModel=s,this._saveConversation=r,this._rulesModel=a,this._state={...se.create(o!=null&&o.forceAgentConversation?{extraData:{isAgentConversation:!0,hasAgentOnboarded:!0}}:void 0)},this._totalCharactersStore=this._createTotalCharactersStore(),this._chatHistorySummarizationModel=new wu(this,e,n)}get conversationId(){return this._state.id}insertChatItem(e,n){const s=[...this._state.chatHistory];s.splice(e,0,n),this.update({chatHistory:s})}_createTotalCharactersStore(){return yl(()=>{let e=0;const n=this._state.chatHistory;return this.convertHistoryToExchanges(n).forEach(s=>{e+=JSON.stringify(s).length}),this._state.draftExchange&&(e+=JSON.stringify(this._state.draftExchange).length),e},0,this._totalCharactersCacheThrottleMs)}async decidePersonaType(){var e;try{return(((e=(await this._extensionClient.getWorkspaceInfo()).trackedFileCount)==null?void 0:e.reduce((s,r)=>s+r,0))||0)<=4?V.PROTOTYPER:V.DEFAULT}catch(n){return console.error("Error determining persona type:",n),V.DEFAULT}}static create(e={}){const n=new Date().toISOString();return{id:e.id||crypto.randomUUID(),name:void 0,createdAtIso:n,lastInteractedAtIso:n,chatHistory:[],feedbackStates:{},toolUseStates:{},draftExchange:void 0,draftActiveContextIds:void 0,selectedModelId:void 0,requestIds:[],isPinned:!1,lastUrl:void 0,isShareable:!1,extraData:{},personaType:V.DEFAULT,...e}}static toSentenceCase(e){return e.charAt(0).toUpperCase()+e.slice(1)}static getDisplayName(e){if(e.name)return e.name;const n=e.chatHistory.find(L);return n&&n.request_message?se.toSentenceCase(n.request_message):mt(e)?"New Agent":"New Chat"}static isNew(e){return e.id===Pl}static isEmpty(e){var r;const n=e.chatHistory.filter(a=>L(a)),s=e.chatHistory.filter(a=>Du(a));return n.length===0&&s.length===0&&!((r=e.draftExchange)!=null&&r.request_message)}static isNamed(e){return e.name!==void 0&&e.name!==""}static getTime(e,n){return n==="lastMessageTimestamp"?se.lastMessageTimestamp(e):n==="lastInteractedAt"?se.lastInteractedAt(e):se.createdAt(e)}static createdAt(e){return new Date(e.createdAtIso)}static lastInteractedAt(e){return new Date(e.lastInteractedAtIso)}static lastMessageTimestamp(e){var s;const n=(s=e.chatHistory.findLast(L))==null?void 0:s.timestamp;return n?new Date(n):this.createdAt(e)}static isValid(e){return e.id!==void 0&&(!se.isEmpty(e)||se.isNamed(e))}onBeforeChangeConversation(e){return this._onBeforeChangeConversationListeners.push(e),()=>{this._onBeforeChangeConversationListeners=this._onBeforeChangeConversationListeners.filter(n=>n!==e)}}_notifyBeforeChangeConversation(e,n){let s=n;for(const r of this._onBeforeChangeConversationListeners){const a=r(e,s);a!==void 0&&(s=a)}return s}get extraData(){return this._state.extraData}set extraData(e){this.update({extraData:e})}get focusModel(){return this._focusModel}get isValid(){return se.isValid(this._state)}get id(){return this._state.id}get name(){return this._state.name}get personaType(){return this._state.personaType??V.DEFAULT}get rootTaskUuid(){return this._state.rootTaskUuid}set rootTaskUuid(e){this.update({rootTaskUuid:e})}get displayName(){return se.getDisplayName(this._state)}get createdAtIso(){return this._state.createdAtIso}get createdAt(){return se.createdAt(this._state)}get chatHistory(){return this._state.chatHistory}get feedbackStates(){return this._state.feedbackStates}get toolUseStates(){return this._state.toolUseStates}get draftExchange(){return this._state.draftExchange}get selectedModelId(){return this._state.selectedModelId}get isPinned(){return!!this._state.isPinned}get extensionClient(){return this._extensionClient}get flags(){return this._chatFlagModel}addChatItem(e){this.addExchange(e)}get requestIds(){return this._state.chatHistory.map(e=>e.request_id).filter(e=>e!==void 0)}get hasDraft(){var s;const e=(((s=this.draftExchange)==null?void 0:s.request_message)??"").trim()!=="",n=this.hasImagesInDraft();return e||n}hasImagesInDraft(){var s;const e=(s=this.draftExchange)==null?void 0:s.rich_text_json_repr;if(!e)return!1;const n=r=>Array.isArray(r)?r.some(n):!!r&&(r.type==="file"||!(!r.content||!Array.isArray(r.content))&&r.content.some(n));return n(e)}get canSendDraft(){return this.hasDraft&&!this.awaitingReply}get canCancelMessage(){return this.awaitingReply}get firstExchange(){return this.chatHistory.find(L)??null}get lastExchange(){return this.chatHistory.findLast(L)??null}get canClearHistory(){return this._state.chatHistory.length!==0&&!this.awaitingReply}get recoverableExchanges(){return this._state.chatHistory.filter(e=>L(e)&&e.status===I.sent)}get successfulMessages(){return this._state.chatHistory.filter(e=>Vn(e)||Et(e)||nt(e))}get totalCharactersStore(){return this._totalCharactersStore}convertHistoryToExchanges(e){if(e.length===0)return[];e=this._chatHistorySummarizationModel.preprocessChatHistory(e);const n=[];for(const s of e)if(Vn(s))n.push(jr(s));else if(nt(s))n.push(jr(s));else if(Et(s)&&s.fromTimestamp!==void 0&&s.toTimestamp!==void 0&&s.revertTarget){const r=Ou(s,1),a={request_message:"",response_text:"",request_id:s.request_id||crypto.randomUUID(),request_nodes:[r],response_nodes:[]};n.push(a)}return n}get awaitingReply(){return this.lastExchange!==null&&this.lastExchange.status===I.sent}get lastInteractedAtIso(){return this._state.lastInteractedAtIso}get draftActiveContextIds(){return this._state.draftActiveContextIds}async sendSilentExchange(e){const n=crypto.randomUUID();let s,r="";const a=await this._addIdeStateNode(Wr({...e,request_id:n,status:I.sent,timestamp:new Date().toISOString()}));for await(const o of this.sendUserMessage(n,a,!0))o.response_text&&(r+=o.response_text),o.request_id&&(s=o.request_id);return{responseText:r,requestId:s}}async*getChatStream(e){e.request_id&&(yield*this._extensionClient.getExistingChatStream(e.request_id,e.lastChunkId,{flags:this._chatFlagModel}))}_createStreamStateHandlers(e,n,s){return[]}_resolveUnresolvedToolUses(e,n,s){var d,m,p;if(e.length===0)return[e,n];const r=e[e.length-1],a=((d=r.response_nodes)==null?void 0:d.filter(h=>h.type===C.TOOL_USE))??[];if(a.length===0)return[e,n];const o=new Set;(m=n.structured_request_nodes)==null||m.forEach(h=>{var f;h.type===G.TOOL_RESULT&&((f=h.tool_result_node)!=null&&f.tool_use_id)&&o.add(h.tool_result_node.tool_use_id)});const i=a.filter(h=>{var _;const f=(_=h.tool_use)==null?void 0:_.tool_use_id;return f&&!o.has(f)});if(i.length===0)return[e,n];const l=i.map((h,f)=>{const _=h.tool_use.tool_use_id;return function(y,v,E,T){const k=Dl(v,y,T);let w;if(k!==void 0)w=k;else{let x;switch(v.phase){case M.runnable:x="Tool was cancelled before running.";break;case M.new:x="Cancelled by user.";break;case M.checkingSafety:x="Tool was cancelled during safety check.";break;case M.running:x="Tool was cancelled while running.";break;case M.cancelling:x="Tool cancellation was interrupted.";break;case M.cancelled:x="Cancelled by user.";break;case M.error:x="Tool execution failed.";break;case M.completed:x="Tool completed but result was unavailable.";break;case M.unknown:default:x="Cancelled by user.",v.phase!==M.unknown&&console.error(`Unexpected tool state phase: ${v.phase}`)}w={tool_use_id:y,content:x,is_error:!0}}return{id:E,type:G.TOOL_RESULT,tool_result_node:w}}(_,this.getToolUseState(r.request_id,_),as(n.structured_request_nodes??[])+f+1,this._chatFlagModel.enableDebugFeatures)});if((p=n.structured_request_nodes)==null?void 0:p.some(h=>h.type===G.TOOL_RESULT))return[e,{...n,structured_request_nodes:[...n.structured_request_nodes??[],...l]}];{const h={request_message:"",response_text:"OK.",request_id:crypto.randomUUID(),structured_request_nodes:l,structured_output_nodes:[],status:I.success,hidden:!0};return s||this.addExchangeBeforeLast(h),[e.concat(this.convertHistoryToExchanges([h])),n]}}async*sendUserMessage(e,n,s,r){const a=await this._sendUserMessageMutex.acquire();try{yield*this._sendUserMessage(e,n,s,r)}finally{a()}}async*_sendUserMessage(e,n,s,r){var p;const a=this._specialContextInputModel.chatActiveContext;let o;if(n.chatHistory!==void 0)o=n.chatHistory;else{let h=this.successfulMessages;if(n.chatItemType===tt.summaryTitle){const f=h.findIndex(_=>_.chatItemType!==tt.agentOnboarding&&ts(_));f!==-1&&(h=h.slice(f))}o=this.convertHistoryToExchanges(h)}this._chatFlagModel.enableParallelTools&&([o,n]=this._resolveUnresolvedToolUses(o,n,s));let i=this.personaType;if(n.structured_request_nodes){const h=n.structured_request_nodes.find(f=>f.type===G.CHANGE_PERSONALITY);h&&h.change_personality_node&&(i=h.change_personality_node.personality_type)}let l=[];if(this._chatFlagModel.enableRules&&this._rulesModel){this._rulesModel.requestRules();const h=xo(this._rulesModel.getCachedRules());l=ks.filterRulesByContext(h,a.ruleFiles||[])}const u={text:n.request_message,chatHistory:o,silent:s,modelId:n.model_id,context:a,userSpecifiedFiles:a.userSpecifiedFiles,externalSourceIds:(p=a.externalSources)==null?void 0:p.map(h=>h.id),disableRetrieval:n.disableRetrieval??!1,disableSelectedCodeDetails:n.disableSelectedCodeDetails??!1,nodes:n.structured_request_nodes,memoriesInfo:n.memoriesInfo,personaType:i,conversationId:this.id,createdTimestamp:Date.now(),requestIdOverride:r,rules:l},d=this._createStreamStateHandlers(e,u,{flags:this._chatFlagModel}),m=this._extensionClient.startChatStreamWithRetry(e,u,{flags:this._chatFlagModel});for await(const h of m){let f=h;e=h.request_id||e;for(const _ of d)f=_.handleChunk(f)??f;yield f}for(const h of d)yield*h.handleComplete();this.updateExchangeById({structured_request_nodes:n.structured_request_nodes},e)}onSendExchange(e){return this._onSendExchangeListeners.push(e),()=>{this._onSendExchangeListeners=this._onSendExchangeListeners.filter(n=>n!==e)}}onNewConversation(e){return this._onNewConversationListeners.push(e),()=>{this._onNewConversationListeners=this._onNewConversationListeners.filter(n=>n!==e)}}onHistoryDelete(e){return this._onHistoryDeleteListeners.push(e),()=>{this._onHistoryDeleteListeners=this._onHistoryDeleteListeners.filter(n=>n!==e)}}updateChatItem(e,n){return this.chatHistory.find(s=>s.request_id===e)===null?(console.warn("No exchange with this request ID found."),!1):(this.update({chatHistory:this.chatHistory.map(s=>s.request_id===e?{...s,...n}:s)}),!0)}async _addIdeStateNode(e){let n,s=(e.structured_request_nodes??[]).filter(r=>r.type!==G.IDE_STATE);try{n=await this._extensionClient.getChatRequestIdeState()}catch(r){console.error("Failed to add IDE state to exchange:",r)}return n?(s=[...s,{id:as(s)+1,type:G.IDE_STATE,ide_state_node:n}],{...e,structured_request_nodes:s}):e}}function Ou(t,e){const n=(Et(t),t.fromTimestamp),s=(Et(t),t.toTimestamp),r=Et(t)&&t.revertTarget!==void 0;return{id:e,type:G.CHECKPOINT_REF,checkpoint_ref_node:{request_id:t.request_id||"",from_timestamp:n,to_timestamp:s,source:r?wo.CHECKPOINT_REVERT:void 0}}}function jr(t){const e=(t.structured_output_nodes??[]).filter(n=>n.type===C.RAW_RESPONSE||n.type===C.TOOL_USE||n.type===C.TOOL_USE_START).map(n=>n.type===C.TOOL_USE_START?{...n,tool_use:{...n.tool_use,input_json:"{}"},type:C.TOOL_USE}:n);return{request_message:t.request_message,response_text:t.response_text??"",request_id:t.request_id||"",request_nodes:t.structured_request_nodes??[],response_nodes:e}}function as(t){return t.length>0?Math.max(...t.map(e=>e.id)):0}function Wr(t){var e;if(t.request_message.length>0&&!((e=t.structured_request_nodes)!=null&&e.some(n=>n.type===G.TEXT))){let n=t.structured_request_nodes??[];return n=[...n,{id:as(n)+1,type:G.TEXT,text_node:{content:t.request_message}}],{...t,structured_request_nodes:n}}return t}const ad="augment-welcome";var I=(t=>(t.draft="draft",t.sent="sent",t.failed="failed",t.success="success",t.cancelled="cancelled",t))(I||{}),Ge=(t=>(t.running="running",t.awaitingUserAction="awaiting-user-action",t.notRunning="not-running",t))(Ge||{}),z=(t=>(t.seen="seen",t.unseen="unseen",t))(z||{}),tt=(t=>(t.signInWelcome="sign-in-welcome",t.generateCommitMessage="generate-commit-message",t.summaryResponse="summary-response",t.summaryTitle="summary-title",t.educateFeatures="educate-features",t.agentOnboarding="agent-onboarding",t.agenticTurnDelimiter="agentic-turn-delimiter",t.agenticRevertDelimiter="agentic-revert-delimiter",t.agenticCheckpointDelimiter="agentic-checkpoint-delimiter",t.exchange="exchange",t.exchangePointer="exchange-pointer",t.historySummary="history-summary",t))(tt||{});function Kr(t){return L(t)||Fu(t)||Uu(t)}function L(t){return!!t&&(t.chatItemType===void 0||t.chatItemType==="agent-onboarding")}function Vn(t){return L(t)&&t.status==="success"}function Du(t){return!!t&&t.chatItemType==="exchange-pointer"}function od(t){return t.chatItemType==="sign-in-welcome"}function Fu(t){return t.chatItemType==="generate-commit-message"}function id(t){return t.chatItemType==="summary-response"}function ld(t){return t.chatItemType==="educate-features"}function Uu(t){return t.chatItemType==="agent-onboarding"}function ud(t){return t.chatItemType==="agentic-turn-delimiter"}function Et(t){return t.chatItemType==="agentic-checkpoint-delimiter"}function nt(t){return t.chatItemType==="history-summary"}function cd(t){return t.revertTarget!==void 0}function dd(t,e){const n=function(r){if(!r)return;const a=r.findLast(o=>Kr(o.turn));return a?a.turn:void 0}(t);if(!((n==null?void 0:n.status)==="success"||(n==null?void 0:n.status)==="failed"||(n==null?void 0:n.status)==="cancelled"))return!1;const s=function(r){return r?r.findLast(o=>{var i;return!((i=o.turn.request_id)!=null&&i.startsWith(Xt))&&Kr(o.turn)}):void 0}(t);return(s==null?void 0:s.turn.request_id)===e.request_id}function md(t){var e;return((e=t.structured_output_nodes)==null?void 0:e.some(n=>n.type===C.TOOL_USE))??!1}function hd(t){var e;return((e=t.structured_request_nodes)==null?void 0:e.some(n=>n.type===G.TOOL_RESULT))??!1}function pd(t){return!(!t||typeof t!="object")&&(!("request_id"in t)||typeof t.request_id=="string")&&(!("seen_state"in t)||t.seen_state==="seen"||t.seen_state==="unseen")}function gd(t){return(t==null?void 0:t.status)==="success"||(t==null?void 0:t.status)==="failed"||(t==null?void 0:t.status)==="cancelled"}function fd(t){if(!t)return;const e=t.filter(n=>L(n.turn)).map(n=>{return"response_text"in(s=n.turn)?s.response_text??"":"";var s}).filter(n=>n.length>0);return e.length>0?e.join(`
`):void 0}function yd(t){let e=[];if(!t)return e;for(const n of t){const s=n.turn;L(s)&&s.structured_output_nodes&&(e=e.concat(s.structured_output_nodes))}return e}function _d(t){var n;let e=new Set;for(const s of t)if(s.type===C.TOOL_USE&&((n=s.tool_use)!=null&&n.input_json))try{const r=JSON.parse(s.tool_use.input_json).path;e.add(r)}catch(r){console.error("Failed to parse tool input JSON:",r)}return e.size}function bd(t){var e;return t.type===C.AGENT_MEMORY||t.type===C.TOOL_USE&&((e=t.tool_use)==null?void 0:e.tool_name)==="remember"}function vd(t){var e;return t.type===C.TOOL_USE&&((e=t.tool_use)==null?void 0:e.tool_name)==="view"}function Ed(t){var e;return t.type===C.TOOL_USE&&((e=t.tool_use)==null?void 0:e.tool_name)==="str-replace-editor"}async function*Pu(t,e=1e3){for(;t>0;)yield t,await new Promise(n=>setTimeout(n,Math.min(e,t))),t-=e}class Lu{constructor(e,n,s,r=5,a=4e3,o){c(this,"_isCancelled",!1);this.requestId=e,this.chatMessage=n,this.startStreamFn=s,this.maxRetries=r,this.baseDelay=a,this.flags=o}cancel(){this._isCancelled=!0}async*getStream(){let e=0,n=0,s=!1;try{for(;!this._isCancelled;){const r=this.startStreamFn({...this.chatMessage,createdTimestamp:Date.now()},this.flags?{flags:this.flags}:void 0);let a,o,i=!1,l=!0;for await(const u of r){if(u.status===I.failed){if(u.isRetriable!==!0||s)return yield u;i=!0,l=u.shouldBackoff??!0,a=u.display_error_message,o=u.request_id;break}s=!0,yield u}if(!i)return;if(this._isCancelled)return yield this.createCancelledStatus();if(e++,e>this.maxRetries)return console.error(`Failed after ${this.maxRetries} attempts: ${a}`),void(yield{request_id:o??this.requestId,seen_state:z.unseen,status:I.failed,display_error_message:a,isRetriable:!1});if(l){const u=this.baseDelay*2**n;n++;for await(const d of Pu(u))yield{request_id:this.requestId,status:I.sent,display_error_message:`Service temporarily unavailable. Retrying in ${Math.floor(d/1e3)} seconds... (Attempt ${e} of ${this.maxRetries})`,isRetriable:!0}}yield{request_id:this.requestId,status:I.sent,display_error_message:`Generating response... (Attempt ${e+1})`,isRetriable:!0}}this._isCancelled&&(yield this.createCancelledStatus())}catch(r){console.error("Unexpected error in chat stream:",r),yield{request_id:this.requestId,seen_state:z.unseen,status:I.failed,display_error_message:r instanceof Error?r.message:String(r)}}}createCancelledStatus(){return{request_id:this.requestId,seen_state:z.unseen,status:I.cancelled}}}var et=(t=>(t.getHydratedTaskRequest="get-hydrated-task-request",t.getHydratedTaskResponse="get-hydrated-task-response",t.setCurrentRootTaskUuid="set-current-root-task-uuid",t.createTaskRequest="create-task-request",t.createTaskResponse="create-task-response",t.updateTaskRequest="update-task-request",t.updateTaskResponse="update-task-response",t.updateHydratedTaskRequest="update-hydrated-task-request",t.updateHydratedTaskResponse="update-hydrated-task-response",t))(et||{});class $u{constructor(e){c(this,"getHydratedTask",async e=>{const n={type:et.getHydratedTaskRequest,data:{uuid:e}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.task});c(this,"createTask",async(e,n,s)=>{const r={type:et.createTaskRequest,data:{name:e,description:n,parentTaskUuid:s}};return(await this._asyncMsgSender.sendToSidecar(r,3e4)).data.uuid});c(this,"updateTask",async(e,n,s)=>{const r={type:et.updateTaskRequest,data:{uuid:e,updates:n,updatedBy:s}};await this._asyncMsgSender.sendToSidecar(r,3e4)});c(this,"setCurrentRootTaskUuid",e=>{const n={type:et.setCurrentRootTaskUuid,data:{uuid:e}};this._asyncMsgSender.sendToSidecar(n)});c(this,"updateHydratedTask",async(e,n)=>{const s={type:et.updateHydratedTaskRequest,data:{task:e,updatedBy:n}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});this._asyncMsgSender=e}}var ve=(t=>(t.getRulesListRequest="get-rules-list-request",t.getRulesListResponse="get-rules-list-response",t.createRule="create-rule",t.createRuleResponse="create-rule-response",t.openRule="open-rule",t.openGuidelines="open-guidelines",t.deleteRule="delete-rule",t.updateRuleFile="update-rule-file",t.updateRuleFileResponse="update-rule-file-response",t.getWorkspaceRoot="get-workspace-root",t.getWorkspaceRootResponse="get-workspace-root-response",t.autoImportRules="auto-import-rules",t.autoImportRulesOptionsResponse="auto-import-rules-options-response",t.autoImportRulesSelectionRequest="auto-import-rules-selection-request",t.autoImportRulesResponse="auto-import-rules-response",t.processSelectedPathsRequest="process-selected-paths-request",t.processSelectedPathsResponse="process-selected-paths-response",t))(ve||{}),Jt=(t=>(t.loadConversationToolUseStatesRequest="load-conversation-tooluse-states-request",t.loadConversationToolUseStatesResponse="load-conversation-tooluse-states-response",t.saveToolUseStatesRequest="save-tooluse-states-request",t.saveToolUseStatesResponse="save-tooluse-states-response",t.deleteConversationToolUseStatesRequest="delete-conversation-tooluse-states-request",t.deleteConversationToolUseStatesResponse="delete-conversation-tooluse-states-response",t))(Jt||{});class Td{constructor(e,n,s){c(this,"_taskClient");c(this,"getChatInitData",async()=>{const e=await this._asyncMsgSender.send({type:b.chatLoaded},3e4);if(e.data.enableDebugFeatures)try{console.log("Running hello world test...");const n=await async function(s){return(await Ki(Xi,new Ka({sendMessage:a=>{s.postMessage(a)},onReceiveMessage:a=>{const o=i=>{a(i.data)};return window.addEventListener("message",o),()=>{window.removeEventListener("message",o)}}})).testMethod({foo:"bar"},{timeoutMs:1e3})).result}(this._host);console.log("Hello world result:",n)}catch(n){console.error("Hello world error:",n)}return e.data});c(this,"reportWebviewClientEvent",e=>{this._asyncMsgSender.send({type:b.reportWebviewClientMetric,data:{webviewName:Xa.chat,client_metric:e,value:1}})});c(this,"trackEventWithTypes",(e,n)=>{this._asyncMsgSender.send({type:b.trackAnalyticsEvent,data:{eventName:e,properties:n}})});c(this,"reportAgentSessionEvent",e=>{this._asyncMsgSender.sendToSidecar({type:q.reportAgentSessionEvent,data:e})});c(this,"reportAgentRequestEvent",e=>{this._asyncMsgSender.sendToSidecar({type:q.reportAgentRequestEvent,data:e})});c(this,"getSuggestions",async(e,n=!1)=>{const s={rootPath:"",relPath:e},r=this.findFiles(s,6),a=this.findRecentlyOpenedFiles(s,6),o=this.findFolders(s,3),i=this.findExternalSources(e,n),l=this._flags.enableRules?this.findRules(e,6):Promise.resolve([]),[u,d,m,p,h]=await Promise.all([ht(r,[]),ht(a,[]),ht(o,[]),ht(i,[]),ht(l,[])]),f=(y,v)=>({...Rl(y),[v]:y}),_=[...u.map(y=>f(y,"file")),...m.map(y=>f(y,"folder")),...d.map(y=>f(y,"recentFile")),...p.map(y=>({label:y.name,name:y.name,id:y.id,externalSource:y})),...h.map(y=>({...Cl(y),rule:y}))];if(this._flags.enablePersonalities){const y=this.getPersonalities(e);y.length>0&&_.push(...y)}return _});c(this,"getPersonalities",e=>{if(!this._flags.enablePersonalities)return[];if(e==="")return kr;const n=e.toLowerCase();return kr.filter(s=>{const r=s.personality.description.toLowerCase(),a=s.label.toLowerCase();return r.includes(n)||a.includes(n)})});c(this,"sendAction",e=>{this._host.postMessage({type:b.mainPanelPerformAction,data:e})});c(this,"showAugmentPanel",()=>{this._asyncMsgSender.send({type:b.showAugmentPanel})});c(this,"showNotification",e=>{this._host.postMessage({type:b.showNotification,data:e})});c(this,"openConfirmationModal",async e=>(await this._asyncMsgSender.send({type:b.openConfirmationModal,data:e},1e9)).data.ok);c(this,"clearMetadataFor",e=>{this._host.postMessage({type:b.chatClearMetadata,data:e})});c(this,"resolvePath",async(e,n=void 0)=>{const s=await this._asyncMsgSender.send({type:b.resolveFileRequest,data:{...e,exactMatch:!0,maxResults:1,searchScope:n}},5e3);if(s.data)return s.data});c(this,"resolveSymbols",async(e,n)=>(await this._asyncMsgSender.send({type:b.findSymbolRequest,data:{query:e,searchScope:n}},3e4)).data);c(this,"getDiagnostics",async()=>(await this._asyncMsgSender.send({type:b.getDiagnosticsRequest},1e3)).data);c(this,"findFiles",async(e,n=12)=>(await this._asyncMsgSender.send({type:b.findFileRequest,data:{...e,maxResults:n}},5e3)).data);c(this,"findFolders",async(e,n=12)=>(await this._asyncMsgSender.send({type:b.findFolderRequest,data:{...e,maxResults:n}},5e3)).data);c(this,"findRecentlyOpenedFiles",async(e,n=12)=>(await this._asyncMsgSender.send({type:b.findRecentlyOpenedFilesRequest,data:{...e,maxResults:n}},5e3)).data);c(this,"findExternalSources",async(e,n=!1)=>this._flags.enableExternalSourcesInChat?n?[]:(await this._asyncMsgSender.send({type:b.findExternalSourcesRequest,data:{query:e,source_types:[]}},5e3)).data.sources??[]:[]);c(this,"findRules",async(e,n=12)=>(await this._asyncMsgSender.sendToSidecar({type:ve.getRulesListRequest,data:{query:e,maxResults:n}})).data.rules);c(this,"openFile",e=>{this._host.postMessage({type:b.openFile,data:e})});c(this,"saveFile",e=>this._host.postMessage({type:b.saveFile,data:e}));c(this,"loadFile",e=>this._host.postMessage({type:b.loadFile,data:e}));c(this,"openMemoriesFile",()=>{this._host.postMessage({type:b.openMemoriesFile})});c(this,"canShowTerminal",async(e,n)=>{try{return(await this._asyncMsgSender.send({type:b.canShowTerminal,data:{terminalId:e,command:n}},5e3)).data.canShow}catch(s){return console.error("Failed to check if terminal can be shown:",s),!1}});c(this,"showTerminal",async(e,n)=>{try{return(await this._asyncMsgSender.send({type:b.showTerminal,data:{terminalId:e,command:n}},5e3)).data.success}catch(s){return console.error("Failed to show terminal:",s),!1}});c(this,"createFile",(e,n)=>{this._host.postMessage({type:b.chatCreateFile,data:{code:e,relPath:n}})});c(this,"openScratchFile",async(e,n="shellscript")=>{await this._asyncMsgSender.send({type:b.openScratchFileRequest,data:{content:e,language:n}},1e4)});c(this,"resolveWorkspaceFileChunk",async e=>{try{return(await this._asyncMsgSender.send({type:b.resolveWorkspaceFileChunkRequest,data:e},5e3)).data}catch{return}});c(this,"smartPaste",e=>{this._host.postMessage({type:b.chatSmartPaste,data:e})});c(this,"getHydratedTask",async e=>this._taskClient.getHydratedTask(e));c(this,"updateHydratedTask",async(e,n)=>this._taskClient.updateHydratedTask(e,n));c(this,"setCurrentRootTaskUuid",e=>{this._taskClient.setCurrentRootTaskUuid(e)});c(this,"createTask",async(e,n,s)=>this._taskClient.createTask(e,n,s));c(this,"updateTask",async(e,n,s)=>this._taskClient.updateTask(e,n,s));c(this,"saveChat",async(e,n,s)=>this._asyncMsgSender.send({type:b.saveChat,data:{conversationId:e,chatHistory:n,title:s}},5e3));c(this,"updateUserGuidelines",e=>{this._host.postMessage({type:b.updateUserGuidelines,data:e})});c(this,"updateWorkspaceGuidelines",e=>{this._host.postMessage({type:b.updateWorkspaceGuidelines,data:e})});c(this,"openSettingsPage",e=>{this._host.postMessage({type:b.openSettingsPage,data:e})});c(this,"_activeRetryStreams",new Map);c(this,"cancelChatStream",async e=>{var n;(n=this._activeRetryStreams.get(e))==null||n.cancel(),await this._asyncMsgSender.send({type:b.chatUserCancel,data:{requestId:e}},1e4)});c(this,"sendUserRating",async(e,n,s,r="")=>{const a={requestId:e,rating:s,note:r,mode:n},o={type:b.chatRating,data:a};return(await this._asyncMsgSender.send(o,3e4)).data});c(this,"triggerUsedChatMetric",()=>{this._host.postMessage({type:b.usedChat})});c(this,"createProject",e=>{this._host.postMessage({type:b.mainPanelCreateProject,data:{name:e}})});c(this,"openProjectFolder",()=>{this._host.postMessage({type:b.mainPanelPerformAction,data:"open-folder"})});c(this,"closeProjectFolder",()=>{this._host.postMessage({type:b.mainPanelPerformAction,data:"close-folder"})});c(this,"cloneRepository",()=>{this._host.postMessage({type:b.mainPanelPerformAction,data:"clone-repository"})});c(this,"grantSyncPermission",()=>{this._host.postMessage({type:b.mainPanelPerformAction,data:"grant-sync-permission"})});c(this,"startRemoteMCPAuth",e=>{this._host.postMessage({type:b.startRemoteMCPAuth,data:{name:e}})});c(this,"callTool",async(e,n,s,r,a,o)=>{const i={type:b.callTool,data:{chatRequestId:e,toolUseId:n,name:s,input:r,chatHistory:a,conversationId:o}};return(await this._asyncMsgSender.send(i,0)).data});c(this,"cancelToolRun",async(e,n)=>{const s={type:b.cancelToolRun,data:{requestId:e,toolUseId:n}};await this._asyncMsgSender.send(s,0)});c(this,"checkSafe",async e=>{const n={type:Kt.checkToolCallSafeRequest,data:e};return(await this._asyncMsgSender.sendToSidecar(n,0)).data});c(this,"closeAllToolProcesses",async()=>{await this._asyncMsgSender.sendToSidecar({type:Kt.closeAllToolProcesses},0)});c(this,"getToolIdentifier",async e=>{const n={type:Kt.getToolIdentifierRequest,data:{toolName:e}};return(await this._asyncMsgSender.sendToSidecar(n,0)).data});c(this,"getChatMode",async()=>{const e={type:q.getChatModeRequest};return(await this._asyncMsgSender.sendToSidecar(e,3e4)).data.chatMode});c(this,"setChatMode",e=>{this._asyncMsgSender.send({type:b.chatModeChanged,data:{mode:e}})});c(this,"getAgentEditList",async(e,n)=>{const s={type:q.getEditListRequest,data:{fromTimestamp:e,toTimestamp:n}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});c(this,"hasChangesSince",async e=>{const n={type:q.getEditListRequest,data:{fromTimestamp:e,toTimestamp:Number.MAX_SAFE_INTEGER}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.edits.filter(s=>{var r,a;return((r=s.changesSummary)==null?void 0:r.totalAddedLines)||((a=s.changesSummary)==null?void 0:a.totalRemovedLines)}).length>0});c(this,"getToolCallCheckpoint",async e=>{const n={type:b.getToolCallCheckpoint,data:{requestId:e}};return(await this._asyncMsgSender.send(n,3e4)).data.checkpointNumber});c(this,"setCurrentConversation",e=>{this._asyncMsgSender.sendToSidecar({type:q.setCurrentConversation,data:{conversationId:e}})});c(this,"migrateConversationId",async(e,n)=>{await this._asyncMsgSender.sendToSidecar({type:q.migrateConversationId,data:{oldConversationId:e,newConversationId:n}},3e4)});c(this,"showAgentReview",(e,n,s,r=!0,a)=>{this._asyncMsgSender.sendToSidecar({type:q.chatReviewAgentFile,data:{qualifiedPathName:e,fromTimestamp:n,toTimestamp:s,retainFocus:r,useNativeDiffIfAvailable:a}})});c(this,"acceptAllAgentEdits",async()=>(await this._asyncMsgSender.sendToSidecar({type:q.chatAgentEditAcceptAll}),!0));c(this,"revertToTimestamp",async(e,n)=>(await this._asyncMsgSender.sendToSidecar({type:q.revertToTimestamp,data:{timestamp:e,qualifiedPathNames:n}}),!0));c(this,"getAgentOnboardingPrompt",async()=>(await this._asyncMsgSender.send({type:b.chatGetAgentOnboardingPromptRequest,data:{}},3e4)).data.prompt);c(this,"getAgentEditChangesByRequestId",async e=>{const n={type:q.getEditChangesByRequestIdRequest,data:{requestId:e}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data});c(this,"getAgentEditContentsByRequestId",async e=>{const n={type:q.getAgentEditContentsByRequestId,data:{requestId:e}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data});c(this,"triggerInitialOrientation",()=>{this._host.postMessage({type:b.triggerInitialOrientation})});c(this,"getWorkspaceInfo",async()=>{try{return(await this._asyncMsgSender.send({type:b.getWorkspaceInfoRequest},5e3)).data}catch(e){return console.error("Error getting workspace info:",e),{}}});c(this,"toggleCollapseUnchangedRegions",()=>{this._host.postMessage({type:b.toggleCollapseUnchangedRegions})});c(this,"checkAgentAutoModeApproval",async()=>(await this._asyncMsgSender.send({type:b.checkAgentAutoModeApproval},5e3)).data);c(this,"setAgentAutoModeApproved",async e=>{await this._asyncMsgSender.send({type:b.setAgentAutoModeApproved,data:e},5e3)});c(this,"checkHasEverUsedAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:q.checkHasEverUsedAgent},5e3)).data);c(this,"setHasEverUsedAgent",async e=>{await this._asyncMsgSender.sendToSidecar({type:q.setHasEverUsedAgent,data:e},5e3)});c(this,"checkHasEverUsedRemoteAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:q.checkHasEverUsedRemoteAgent},5e3)).data);c(this,"setHasEverUsedRemoteAgent",async e=>{await this._asyncMsgSender.sendToSidecar({type:q.setHasEverUsedRemoteAgent,data:e},5e3)});c(this,"getChatRequestIdeState",async()=>{const e={type:b.getChatRequestIdeStateRequest};return(await this._asyncMsgSender.send(e,3e4)).data});c(this,"reportError",e=>{this._host.postMessage({type:b.reportError,data:e})});c(this,"sendMemoryCreated",async e=>{await this._asyncMsgSender.sendToSidecar(e,5e3)});c(this,"sendGitMessage",async e=>await this._asyncMsgSender.sendToSidecar(e,3e4));this._host=e,this._asyncMsgSender=n,this._flags=s,this._taskClient=new $u(n)}async*generateCommitMessage(){const e={type:b.generateCommitMessage},n=this._asyncMsgSender.stream(e,3e4,6e4);yield*Yn(n,()=>{},this._flags.retryChatStreamTimeouts)}async*sendInstructionMessage(e,n){const s={instruction:e.request_message??"",selectedCodeDetails:n,requestId:e.request_id},r={type:b.chatInstructionMessage,data:s},a=this._asyncMsgSender.stream(r,3e4,6e4);yield*async function*(o){let i;try{for await(const l of o)i=l.data.requestId,yield{request_id:i,response_text:l.data.text,seen_state:z.unseen,status:I.sent};yield{request_id:i,seen_state:z.unseen,status:I.success}}catch(l){console.error("Error in chat instruction model reply stream:",l),yield{request_id:i,seen_state:z.unseen,status:I.failed}}}(a)}async openGuidelines(e){this._host.postMessage({type:b.openGuidelines,data:e})}async*getExistingChatStream(e,n,s){const r=s==null?void 0:s.flags.enablePreferenceCollection,a=r?1e9:6e4,o=r?1e9:3e5,i={type:b.chatGetStreamRequest,data:{requestId:e,lastChunkId:n}},l=this._asyncMsgSender.stream(i,a,o);yield*Yn(l,this.reportError,this._flags.retryChatStreamTimeouts)}async*startChatStream(e,n){const s=n==null?void 0:n.flags.enablePreferenceCollection,r=s?1e9:1e5,a=s?1e9:3e5,o={type:b.chatUserMessage,data:e},i=this._asyncMsgSender.stream(o,r,a);yield*Yn(i,this.reportError,this._flags.retryChatStreamTimeouts)}async checkToolExists(e){return(await this._asyncMsgSender.send({type:b.checkToolExists,toolName:e},0)).exists}async saveImage(e,n){const s=Cr(await Mn(e)),r=n??`${await Rr(await On(s))}.${e.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:b.chatSaveImageRequest,data:{filename:r,data:s}},1e4)).data}async saveAttachment(e,n){const s=Cr(await Mn(e)),r=n??`${await Rr(await On(s))}.${e.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:b.chatSaveAttachmentRequest,data:{filename:r,data:s}},1e4)).data}async loadImage(e){const n=await this._asyncMsgSender.send({type:b.chatLoadImageRequest,data:e},1e4),s=n.data?await On(n.data):void 0;if(!s)return;let r="application/octet-stream";const a=e.split(".").at(-1);a==="png"?r="image/png":a!=="jpg"&&a!=="jpeg"||(r="image/jpeg");const o=new File([s],e,{type:r});return await Mn(o)}async deleteImage(e){await this._asyncMsgSender.send({type:b.chatDeleteImageRequest,data:e},1e4)}async*startChatStreamWithRetry(e,n,s){const r=new Lu(e,n,(a,o)=>this.startChatStream(a,o),(s==null?void 0:s.maxRetries)??5,4e3,s==null?void 0:s.flags);this._activeRetryStreams.set(e,r);try{yield*r.getStream()}finally{this._activeRetryStreams.delete(e)}}async getSubscriptionInfo(){return await this._asyncMsgSender.send({type:b.getSubscriptionInfo},5e3)}async loadExchanges(e,n){if(n.length===0)return[];const s={type:zt.loadExchangesByUuidsRequest,data:{conversationId:e,uuids:n}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data.exchanges}async saveExchanges(e,n){if(n.length===0)return;const s={type:zt.saveExchangesRequest,data:{conversationId:e,exchanges:n}};await this._asyncMsgSender.sendToSidecar(s,3e4)}async deleteConversationExchanges(e){const n={type:zt.deleteConversationExchangesRequest,data:{conversationId:e}};await this._asyncMsgSender.sendToSidecar(n,3e4)}async loadConversationToolUseStates(e){const n={type:Jt.loadConversationToolUseStatesRequest,data:{conversationId:e}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.toolUseStates}async saveToolUseStates(e,n){if(Object.keys(n).length===0)return;const s={type:Jt.saveToolUseStatesRequest,data:{conversationId:e,toolUseStates:n}};await this._asyncMsgSender.sendToSidecar(s,3e4)}async deleteConversationToolUseStates(e){const n={type:Jt.deleteConversationToolUseStatesRequest,data:{conversationId:e}};await this._asyncMsgSender.sendToSidecar(n,3e4)}}async function*Yn(t,e=()=>{},n){let s;try{for await(const r of t){if(s=r.data.requestId,r.data.error)return console.error("Error in chat model reply stream:",r.data.error.displayErrorMessage),yield{request_id:s,seen_state:z.unseen,status:I.failed,display_error_message:r.data.error.displayErrorMessage,isRetriable:r.data.error.isRetriable,shouldBackoff:r.data.error.shouldBackoff};const a={request_id:s,response_text:r.data.text,workspace_file_chunks:r.data.workspaceFileChunks,structured_output_nodes:qu(r.data.nodes),seen_state:z.unseen,status:I.sent,lastChunkId:r.data.chunkId};r.data.stop_reason!=null&&(a.stop_reason=r.data.stop_reason),yield a}yield{request_id:s,seen_state:z.unseen,status:I.success}}catch(r){let a,o;if(e({originalRequestId:s||"",sanitizedMessage:r instanceof Error?r.message:String(r),stackTrace:r instanceof Error&&r.stack||"",diagnostics:[{key:"error_class",value:"Extension-WebView Error"}]}),r instanceof Eo&&n)switch(r.name){case"MessageTimeout":a=!0,o=!1;break;case"StreamTimeout":case"InvalidResponse":a=!1}console.error("Unexpected error in chat model reply stream:",r),yield{request_id:s,seen_state:z.unseen,status:I.failed,isRetriable:a,shouldBackoff:o}}}async function ht(t,e){try{return await t}catch(n){return console.warn(`Error while resolving promise: ${n}`),e}}function qu(t){if(!t)return t;let e=!1;return t.filter(n=>n.type!==C.TOOL_USE||!e&&(e=!0,!0))}const Sd=15,Id=1e3,Hu=25e4,wd=2e4;class Nd{constructor(e){c(this,"_enableEditableHistory",!1);c(this,"_enablePreferenceCollection",!1);c(this,"_enableRetrievalDataCollection",!1);c(this,"_enableDebugFeatures",!1);c(this,"_enableConversationDebugUtils",!1);c(this,"_enableRichTextHistory",!1);c(this,"_enableAgentSwarmMode",!1);c(this,"_modelDisplayNameToId",{});c(this,"_fullFeatured",!0);c(this,"_enableExternalSourcesInChat",!1);c(this,"_smallSyncThreshold",15);c(this,"_bigSyncThreshold",1e3);c(this,"_enableSmartPaste",!1);c(this,"_enableDirectApply",!1);c(this,"_summaryTitles",!1);c(this,"_suggestedEditsAvailable",!1);c(this,"_enableShareService",!1);c(this,"_maxTrackableFileCount",Hu);c(this,"_enableDesignSystemRichTextEditor",!1);c(this,"_enableSources",!1);c(this,"_enableChatMermaidDiagrams",!1);c(this,"_smartPastePrecomputeMode",To.visibleHover);c(this,"_useNewThreadsMenu",!1);c(this,"_enableChatMermaidDiagramsMinVersion",!1);c(this,"_enablePromptEnhancer",!1);c(this,"_idleNewSessionNotificationTimeoutMs");c(this,"_idleNewSessionMessageTimeoutMs");c(this,"_enableChatMultimodal",!1);c(this,"_enableAgentMode",!1);c(this,"_enableAgentAutoMode",!1);c(this,"_enableRichCheckpointInfo",!1);c(this,"_agentMemoriesFilePathName");c(this,"_conversationHistorySizeThresholdBytes",44040192);c(this,"_userTier","unknown");c(this,"_eloModelConfiguration",{highPriorityModels:[],regularBattleModels:[],highPriorityThreshold:.5});c(this,"_truncateChatHistory",!1);c(this,"_enableBackgroundAgents",!1);c(this,"_enableNewThreadsList",!1);c(this,"_customPersonalityPrompts",{});c(this,"_enablePersonalities",!1);c(this,"_enableRules",!1);c(this,"_memoryClassificationOnFirstToken",!1);c(this,"_enableGenerateCommitMessage",!1);c(this,"_modelRegistry",{});c(this,"_enableModelRegistry",!1);c(this,"_enableTaskList",!1);c(this,"_clientAnnouncement","");c(this,"_useHistorySummary",!1);c(this,"_historySummaryParams","");c(this,"_enableExchangeStorage",!1);c(this,"_enableToolUseStateStorage",!1);c(this,"_retryChatStreamTimeouts",!1);c(this,"_enableCommitIndexing",!1);c(this,"_enableMemoryRetrieval",!1);c(this,"_enableAgentTabs",!1);c(this,"_isVscodeVersionOutdated",!1);c(this,"_vscodeMinVersion","");c(this,"_enableGroupedTools",!1);c(this,"_remoteAgentsResumeHintAvailableTtlDays",0);c(this,"_enableParallelTools",!1);c(this,"_enableAgentGitTracker",!1);c(this,"_memoriesParams",{});c(this,"_subscribers",new Set);c(this,"subscribe",e=>(this._subscribers.add(e),e(this),()=>{this._subscribers.delete(e)}));c(this,"update",e=>{this._enableEditableHistory=e.enableEditableHistory??this._enableEditableHistory,this._enablePreferenceCollection=e.enablePreferenceCollection??this._enablePreferenceCollection,this._enableRetrievalDataCollection=e.enableRetrievalDataCollection??this._enableRetrievalDataCollection,this._enableDebugFeatures=e.enableDebugFeatures??this._enableDebugFeatures,this._enableConversationDebugUtils=e.enableConversationDebugUtils??this._enableConversationDebugUtils,this._enableRichTextHistory=e.enableRichTextHistory??this._enableRichTextHistory,this._enableAgentSwarmMode=e.enableAgentSwarmMode??this._enableAgentSwarmMode,this._modelDisplayNameToId={...e.modelDisplayNameToId},this._fullFeatured=e.fullFeatured??this._fullFeatured,this._enableExternalSourcesInChat=e.enableExternalSourcesInChat??this._enableExternalSourcesInChat,this._smallSyncThreshold=e.smallSyncThreshold??this._smallSyncThreshold,this._bigSyncThreshold=e.bigSyncThreshold??this._bigSyncThreshold,this._enableSmartPaste=e.enableSmartPaste??this._enableSmartPaste,this._enableDirectApply=e.enableDirectApply??this._enableDirectApply,this._summaryTitles=e.summaryTitles??this._summaryTitles,this._suggestedEditsAvailable=e.suggestedEditsAvailable??this._suggestedEditsAvailable,this._enableShareService=e.enableShareService??this._enableShareService,this._maxTrackableFileCount=e.maxTrackableFileCount??this._maxTrackableFileCount,this._enableDesignSystemRichTextEditor=e.enableDesignSystemRichTextEditor??this._enableDesignSystemRichTextEditor,this._enableSources=e.enableSources??this._enableSources,this._enableChatMermaidDiagrams=e.enableChatMermaidDiagrams??this._enableChatMermaidDiagrams,this._smartPastePrecomputeMode=e.smartPastePrecomputeMode??this._smartPastePrecomputeMode,this._useNewThreadsMenu=e.useNewThreadsMenu??this._useNewThreadsMenu,this._enableChatMermaidDiagramsMinVersion=e.enableChatMermaidDiagramsMinVersion??this._enableChatMermaidDiagramsMinVersion,this._enablePromptEnhancer=e.enablePromptEnhancer??this._enablePromptEnhancer,this._idleNewSessionMessageTimeoutMs=e.idleNewSessionMessageTimeoutMs??(e.enableDebugFeatures?this._idleNewSessionMessageTimeoutMs??3e5:this._idleNewSessionMessageTimeoutMs),this._idleNewSessionNotificationTimeoutMs=e.idleNewSessionNotificationTimeoutMs??0,this._enableChatMultimodal=e.enableChatMultimodal??this._enableChatMultimodal,this._enableAgentMode=e.enableAgentMode??this._enableAgentMode,this._enableAgentAutoMode=e.enableAgentAutoMode??this._enableAgentAutoMode,this._enableRichCheckpointInfo=e.enableRichCheckpointInfo??this._enableRichCheckpointInfo,this._agentMemoriesFilePathName=e.agentMemoriesFilePathName??this._agentMemoriesFilePathName,this._conversationHistorySizeThresholdBytes=e.conversationHistorySizeThresholdBytes??this._conversationHistorySizeThresholdBytes,this._userTier=e.userTier??this._userTier,this._eloModelConfiguration=e.eloModelConfiguration??this._eloModelConfiguration,this._truncateChatHistory=e.truncateChatHistory??this._truncateChatHistory,this._enableBackgroundAgents=e.enableBackgroundAgents??this._enableBackgroundAgents,this._enableNewThreadsList=e.enableNewThreadsList??this._enableNewThreadsList,this._customPersonalityPrompts=e.customPersonalityPrompts??this._customPersonalityPrompts,this._enablePersonalities=e.enablePersonalities??this._enablePersonalities,this._enableRules=e.enableRules??this._enableRules,this._memoryClassificationOnFirstToken=e.memoryClassificationOnFirstToken??this._memoryClassificationOnFirstToken,this._enableGenerateCommitMessage=e.enableGenerateCommitMessage??this._enableGenerateCommitMessage,this._modelRegistry=e.modelRegistry??this._modelRegistry,this._enableModelRegistry=e.enableModelRegistry??this._enableModelRegistry,this._enableTaskList=e.enableTaskList??this._enableTaskList,this._clientAnnouncement=e.clientAnnouncement??this._clientAnnouncement,this._useHistorySummary=e.useHistorySummary??this._useHistorySummary,this._historySummaryParams=e.historySummaryParams??this._historySummaryParams,this._enableExchangeStorage=e.enableExchangeStorage??this._enableExchangeStorage,this._retryChatStreamTimeouts=e.retryChatStreamTimeouts??this._retryChatStreamTimeouts,this._enableCommitIndexing=e.enableCommitIndexing??this._enableCommitIndexing,this._enableMemoryRetrieval=e.enableMemoryRetrieval??this._enableMemoryRetrieval,this._enableAgentTabs=e.enableAgentTabs??this._enableAgentTabs,this._isVscodeVersionOutdated=e.isVscodeVersionOutdated??this._isVscodeVersionOutdated,this._vscodeMinVersion=e.vscodeMinVersion??this._vscodeMinVersion,this._enableGroupedTools=e.enableGroupedTools??this._enableGroupedTools,this._remoteAgentsResumeHintAvailableTtlDays=e.remoteAgentsResumeHintAvailableTtlDays??this._remoteAgentsResumeHintAvailableTtlDays,this._enableToolUseStateStorage=e.enableToolUseStateStorage??this._enableToolUseStateStorage,this._enableParallelTools=e.enableParallelTools??this._enableParallelTools,this._enableAgentGitTracker=e.enableAgentGitTracker??this._enableAgentGitTracker,this._memoriesParams=e.memoriesParams??this._memoriesParams,this._subscribers.forEach(n=>n(this))});c(this,"isModelIdValid",e=>e!==void 0&&(Object.values(this._modelDisplayNameToId).includes(e)||Object.values(this._modelRegistry).includes(e??"")));c(this,"getModelDisplayName",e=>{if(e!==void 0)return Object.keys(this._modelDisplayNameToId).find(n=>this._modelDisplayNameToId[n]===e)});e&&this.update(e)}get enableEditableHistory(){return this._fullFeatured&&(this._enableEditableHistory||this._enableDebugFeatures)}get enablePreferenceCollection(){return this._enablePreferenceCollection}get enableRetrievalDataCollection(){return this._enableRetrievalDataCollection}get enableDebugFeatures(){return this._enableDebugFeatures}get enableConversationDebugUtils(){return this._enableConversationDebugUtils||this._enableDebugFeatures}get enableGenerateCommitMessage(){return this._enableGenerateCommitMessage}get enableRichTextHistory(){return this._enableRichTextHistory||this._enableDebugFeatures}get enableAgentSwarmMode(){return this._enableAgentSwarmMode}get modelDisplayNameToId(){return this._modelDisplayNameToId}get orderedModelDisplayNames(){return Object.keys(this._modelDisplayNameToId).sort((e,n)=>{const s=e.toLowerCase(),r=n.toLowerCase();return s==="default"&&r!=="default"?-1:r==="default"&&s!=="default"?1:e.localeCompare(n)})}get fullFeatured(){return this._fullFeatured}get enableExternalSourcesInChat(){return this._enableExternalSourcesInChat}get smallSyncThreshold(){return this._smallSyncThreshold}get bigSyncThreshold(){return this._bigSyncThreshold}get enableSmartPaste(){return this._enableDebugFeatures||this._enableSmartPaste}get enableDirectApply(){return this._enableDirectApply||this._enableDebugFeatures}get enableShareService(){return this._enableShareService}get summaryTitles(){return this._summaryTitles}get suggestedEditsAvailable(){return this._suggestedEditsAvailable}get maxTrackableFileCount(){return this._maxTrackableFileCount}get enableSources(){return this._enableDebugFeatures||this._enableSources}get enableChatMermaidDiagrams(){return this._enableDebugFeatures||this._enableChatMermaidDiagrams}get smartPastePrecomputeMode(){return this._smartPastePrecomputeMode}get useNewThreadsMenu(){return this._useNewThreadsMenu}get enableChatMermaidDiagramsMinVersion(){return this._enableChatMermaidDiagramsMinVersion}get enablePromptEnhancer(){return this._enablePromptEnhancer}get enableDesignSystemRichTextEditor(){return this._enableDesignSystemRichTextEditor}get idleNewSessionNotificationTimeoutMs(){return this._idleNewSessionNotificationTimeoutMs??0}get idleNewSessionMessageTimeoutMs(){return this._idleNewSessionMessageTimeoutMs??0}get enableChatMultimodal(){return this._enableChatMultimodal}get enableAgentMode(){return this._enableAgentMode}get enableAgentAutoMode(){return this._enableAgentAutoMode}get enableRichCheckpointInfo(){return this._enableRichCheckpointInfo}get agentMemoriesFilePathName(){return this._agentMemoriesFilePathName}get conversationHistorySizeThresholdBytes(){return this._conversationHistorySizeThresholdBytes}get userTier(){return this._userTier}get eloModelConfiguration(){return this._eloModelConfiguration}get truncateChatHistory(){return this._truncateChatHistory}get enableBackgroundAgents(){return this._enableBackgroundAgents}get enableNewThreadsList(){return this._enableNewThreadsList}get customPersonalityPrompts(){return this._customPersonalityPrompts}get enablePersonalities(){return this._enablePersonalities||this._enableDebugFeatures}get enableRules(){return this._enableRules}get memoryClassificationOnFirstToken(){return this._memoryClassificationOnFirstToken}get modelRegistry(){return this._modelRegistry}get enableModelRegistry(){return this._enableModelRegistry}get enableTaskList(){return this._enableTaskList}get clientAnnouncement(){return this._clientAnnouncement}get useHistorySummary(){return this._useHistorySummary}get historySummaryParams(){return this._historySummaryParams}get enableExchangeStorage(){return this._enableExchangeStorage}get enableToolUseStateStorage(){return this._enableToolUseStateStorage}get retryChatStreamTimeouts(){return this._retryChatStreamTimeouts}get enableCommitIndexing(){return this._enableCommitIndexing}get enableMemoryRetrieval(){return this._enableMemoryRetrieval}get enableAgentTabs(){return this._enableAgentTabs}get isVscodeVersionOutdated(){return this._isVscodeVersionOutdated}get vscodeMinVersion(){return this._vscodeMinVersion}get enableErgonomicsUpdate(){return this._enableDebugFeatures}get enableGroupedTools(){return this._enableGroupedTools}get remoteAgentsResumeHintAvailableTtlDays(){return this._remoteAgentsResumeHintAvailableTtlDays}get enableParallelTools(){return this._enableParallelTools}get enableAgentGitTracker(){return this._enableAgentGitTracker}get memoriesParams(){return this._memoriesParams}}var Gu=Uo,Bu=/\s/,Vu=function(t){for(var e=t.length;e--&&Bu.test(t.charAt(e)););return e},Yu=/^\s+/,ju=Lo,Wu=Po,Ku=function(t){return t&&t.slice(0,Vu(t)+1).replace(Yu,"")},zr=is,zu=function(t){return typeof t=="symbol"||Wu(t)&&ju(t)=="[object Symbol]"},Xu=/^[-+]0x[0-9a-f]+$/i,Ju=/^0b[01]+$/i,Zu=/^0o[0-7]+$/i,Qu=parseInt,ec=is,jn=function(){return Gu.Date.now()},Xr=function(t){if(typeof t=="number")return t;if(zu(t))return NaN;if(zr(t)){var e=typeof t.valueOf=="function"?t.valueOf():t;t=zr(e)?e+"":e}if(typeof t!="string")return t===0?t:+t;t=Ku(t);var n=Ju.test(t);return n||Zu.test(t)?Qu(t.slice(2),n?2:8):Xu.test(t)?NaN:+t},tc=Math.max,nc=Math.min,sc=function(t,e,n){var s,r,a,o,i,l,u=0,d=!1,m=!1,p=!0;if(typeof t!="function")throw new TypeError("Expected a function");function h(E){var T=s,k=r;return s=r=void 0,u=E,o=t.apply(k,T)}function f(E){var T=E-l;return l===void 0||T>=e||T<0||m&&E-u>=a}function _(){var E=jn();if(f(E))return y(E);i=setTimeout(_,function(T){var k=e-(T-l);return m?nc(k,a-(T-u)):k}(E))}function y(E){return i=void 0,p&&s?h(E):(s=r=void 0,o)}function v(){var E=jn(),T=f(E);if(s=arguments,r=this,l=E,T){if(i===void 0)return function(k){return u=k,i=setTimeout(_,e),d?h(k):o}(l);if(m)return clearTimeout(i),i=setTimeout(_,e),h(l)}return i===void 0&&(i=setTimeout(_,e)),o}return e=Xr(e)||0,ec(n)&&(d=!!n.leading,a=(m="maxWait"in n)?tc(Xr(n.maxWait)||0,e):a,p="trailing"in n?!!n.trailing:p),v.cancel=function(){i!==void 0&&clearTimeout(i),u=0,s=l=r=i=void 0},v.flush=function(){return i===void 0?o:y(jn())},v},rc=is;const ac=ea(function(t,e,n){var s=!0,r=!0;if(typeof t!="function")throw new TypeError("Expected a function");return rc(n)&&(s="leading"in n?!!n.leading:s,r="trailing"in n?!!n.trailing:r),sc(t,e,{leading:s,maxWait:e,trailing:r})});class oc{constructor(e){c(this,"SIDECAR_TIMEOUT_MS",5e3);c(this,"getRulesList",async(e=!0)=>{const n={type:ve.getRulesListRequest,data:{includeGuidelines:e}};return(await this._asyncMsgSender.sendToSidecar(n,this.SIDECAR_TIMEOUT_MS)).data.rules});c(this,"createRule",async e=>{const n={type:ve.createRule,data:{ruleName:e.trim()}};return(await this._asyncMsgSender.sendToSidecar(n,this.SIDECAR_TIMEOUT_MS)).data.createdRule||null});c(this,"getWorkspaceRoot",async()=>{const e={type:ve.getWorkspaceRoot};return(await this._asyncMsgSender.sendToSidecar(e,this.SIDECAR_TIMEOUT_MS)).data.workspaceRoot||""});c(this,"updateRuleFile",async(e,n)=>{const s={type:ve.updateRuleFile,data:{path:e,content:n}};await this._asyncMsgSender.sendToSidecar(s,this.SIDECAR_TIMEOUT_MS)});c(this,"deleteRule",async(e,n=!0)=>{const s={type:ve.deleteRule,data:{path:e,confirmed:n}};await this._asyncMsgSender.sendToSidecar(s,this.SIDECAR_TIMEOUT_MS)});c(this,"processSelectedPaths",async(e,n=!0)=>{const s={type:ve.processSelectedPathsRequest,data:{selectedPaths:e,autoImport:n}},r=await this._asyncMsgSender.sendToSidecar(s,this.SIDECAR_TIMEOUT_MS);return{importedRulesCount:r.data.importedRulesCount,directoryOrFile:r.data.directoryOrFile,errors:r.data.errors}});c(this,"getAutoImportOptions",async()=>{const e={type:ve.autoImportRules};return await this._asyncMsgSender.sendToSidecar(e,this.SIDECAR_TIMEOUT_MS)});c(this,"processAutoImportSelection",async e=>{const n={type:ve.autoImportRulesSelectionRequest,data:{selectedLabel:e}},s=await this._asyncMsgSender.sendToSidecar(n,this.SIDECAR_TIMEOUT_MS);return{importedRulesCount:s.data.importedRulesCount,duplicatesCount:s.data.duplicatesCount,totalAttempted:s.data.totalAttempted,source:s.data.source}});this._asyncMsgSender=e}}class Rd{constructor(e,n=!0){c(this,"_rulesFiles",St([]));c(this,"_loading",St(!0));c(this,"_extensionClientRules");c(this,"_requestRulesThrottled",ac(async()=>{this._loading.set(!0);try{const e=await this._extensionClientRules.getRulesList(this.includeGuidelines);this._rulesFiles.set(e)}catch(e){console.error("Failed to get rules list:",e)}finally{this._loading.set(!1)}},250,{leading:!0,trailing:!0}));this._msgBroker=e,this.includeGuidelines=n,this._extensionClientRules=new oc(this._msgBroker),this.requestRules()}handleMessageFromExtension(e){return!(!e.data||e.data.type!==b.getRulesListResponse)&&(this._rulesFiles.set(e.data.data),this._loading.set(!1),!0)}async requestRules(){return this._requestRulesThrottled()}async createRule(e){try{const n=await this._extensionClientRules.createRule(e);return await this.requestRules(),n}catch(n){throw console.error("Failed to create rule:",n),n}}async getWorkspaceRoot(){try{return await this._extensionClientRules.getWorkspaceRoot()}catch(e){return console.error("Failed to get workspace root:",e),""}}async updateRuleContent(e){const n=Ye.formatRuleFileForMarkdown(e);try{await this._extensionClientRules.updateRuleFile(e.path,n)}catch(s){console.error("Failed to update rule file:",s)}await this.requestRules()}async deleteRule(e){try{await this._extensionClientRules.deleteRule(e,!0),await this.requestRules()}catch(n){throw console.error("Failed to delete rule:",n),n}}async processSelectedPaths(e){try{const n=await this._extensionClientRules.processSelectedPaths(e,!0);return await this.requestRules(),n}catch(n){throw console.error("Failed to process selected paths:",n),n}}async getAutoImportOptions(){return await this._extensionClientRules.getAutoImportOptions()}async processAutoImportSelection(e){try{const n=await this._extensionClientRules.processAutoImportSelection(e.label);return await this.requestRules(),n}catch(n){throw console.error("Failed to process auto-import selection:",n),n}}getCachedRules(){return this._rulesFiles}getLoading(){return this._loading}}var ic=ta('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.84182 3.13514C9.04327 3.32401 9.05348 3.64042 8.86462 3.84188L5.43521 7.49991L8.86462 11.1579C9.05348 11.3594 9.04327 11.6758 8.84182 11.8647C8.64036 12.0535 8.32394 12.0433 8.13508 11.8419L4.38508 7.84188C4.20477 7.64955 4.20477 7.35027 4.38508 7.15794L8.13508 3.15794C8.32394 2.95648 8.64036 2.94628 8.84182 3.13514Z" fill="currentColor"></path></svg>');function lc(t){var e=ic();O(t,e)}const Tt=class Tt{constructor(e=void 0){c(this,"_lastFocusAnchorElement");c(this,"_focusedIndexStore",St(void 0));c(this,"focusedIndex",this._focusedIndexStore);c(this,"_rootElement");c(this,"_triggerElement");c(this,"_getItems",()=>{var s;const e=(s=this._rootElement)==null?void 0:s.querySelectorAll(`.${Tt.ITEM_CLASS}`),n=e==null?void 0:e[0];return n instanceof HTMLElement&&this._recomputeFocusAnchor(n),Array.from(e??[])});c(this,"_recomputeFocusAnchor",e=>{var a;const n=(a=this._parentContext)==null?void 0:a._getItems(),s=n==null?void 0:n.indexOf(e);if(s===void 0||n===void 0)return;const r=Math.max(s-1,0);this._lastFocusAnchorElement=n[r]});c(this,"registerRoot",e=>{this._rootElement=e,e.addEventListener("keydown",this._onKeyDown);const n=()=>{this.getCurrentFocusedIdx()},s=r=>{e.contains(r.relatedTarget)||this._focusedIndexStore.set(void 0)};return e.addEventListener("focusin",n),e.addEventListener("focusout",s),this._getItems(),{destroy:()=>{this._rootElement=void 0,e.removeEventListener("keydown",this._onKeyDown),e.removeEventListener("focusin",n),e.removeEventListener("focusout",s),this._focusedIndexStore.set(void 0)}}});c(this,"registerTrigger",e=>(this._triggerElement=e.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])')??e,{destroy:()=>{this._triggerElement=void 0}}));c(this,"_onKeyDown",e=>{var n;switch(e.key){case"ArrowUp":e.preventDefault(),this.focusPrev();break;case"ArrowDown":e.preventDefault(),this.focusNext();break;case"ArrowLeft":this._requestClose();break;case"ArrowRight":this.clickFocusedItem();break;case"Tab":{const s=this.getCurrentFocusedIdx();if(s===void 0||this.parentContext)break;(!e.shiftKey&&s===this._getItems().length-1||e.shiftKey&&s===0)&&(e.preventDefault(),(n=this._triggerElement)==null||n.focus());break}}});c(this,"_requestClose",()=>{var e;(e=this._rootElement)==null||e.dispatchEvent(new $o)});c(this,"getCurrentFocusedIdx",()=>{const e=this._getItems().findIndex(s=>s===document.activeElement),n=e===-1?void 0:e;return this._focusedIndexStore.set(n),n});c(this,"setFocusedIdx",e=>{const n=this._getItems();if(n.length===0)return void this._focusedIndexStore.set(void 0);const s=qt(e,n.length);this._focusedIndexStore.set(s)});c(this,"focusIdx",e=>{const n=this._getItems();if(n.length===0)return void this._focusedIndexStore.set(void 0);const s=qt(e,n.length),r=n[s];r==null||r.focus(),this._focusedIndexStore.set(s)});c(this,"popNestedFocus",()=>{if(this._parentContext){this._focusedIndexStore.set(void 0);const e=this._lastFocusAnchorElement,n=e?this._parentContext._getItems().indexOf(e):void 0;return n===void 0?(this._parentContext.focusIdx(0),!0):(this._parentContext.focusIdx(n),!0)}return!1});c(this,"focusNext",()=>{const e=this._getItems();if(e.length===0)return;const n=qt(e.findIndex(s=>s===document.activeElement)+1,e.length);e[n].focus(),this._focusedIndexStore.set(n)});c(this,"focusPrev",()=>{var s;const e=this._getItems();if(e.length===0)return;const n=qt(e.findIndex(r=>r===document.activeElement)-1,e.length);(s=e[n])==null||s.focus(),this._focusedIndexStore.set(n)});c(this,"clickFocusedItem",async()=>{const e=document.activeElement;e&&(e.click(),await Kn())});this._parentContext=e}get rootElement(){return this._rootElement}get triggerElement(){return this._triggerElement}get parentContext(){return this._parentContext}};c(Tt,"CONTEXT_KEY","augment-dropdown-menu-focus"),c(Tt,"ITEM_CLASS","js-dropdown-menu__focusable-item");let ie=Tt;function qt(t,e){return(t%e+e)%e}const st="augment-dropdown-menu-content";var uc=J("<div><!></div>"),cc=J('<div class="l-dropdown-menu-augment__container svelte-o54ind"><!></div>');function Jr(t,e){he(e,!1);const[n,s]=Me(),r=()=>Ee(h,"$sizeState",n),a=Ie();let o=N(e,"size",8,2),i=N(e,"onEscapeKeyDown",8,()=>{}),l=N(e,"onClickOutside",8,()=>{}),u=N(e,"onRequestClose",8,()=>{}),d=N(e,"side",8,"top"),m=N(e,"align",8,"center");const p={size:St(o())},h=p.size;na(st,p);const f=Q(ie.CONTEXT_KEY),_=Q(Ht.CONTEXT_KEY);Se(()=>Pe(o()),()=>{h.set(o())}),Se(()=>{},()=>{Mo(we(a,_.state),"$openState",n)}),rt(),pe(),_e("keydown",Ao,function(y){if(Ee(F(a),"$openState",n).open&&y.key==="Tab"&&!y.shiftKey){if(f.getCurrentFocusedIdx()!==void 0)return;y.preventDefault(),f==null||f.focusIdx(0)}}),qo(t,{onEscapeKeyDown:i(),onClickOutside:l(),onRequestClose:u(),get side(){return d()},get align(){return m()},$$events:{keydown(y){Ce.call(this,e,y)}},children:(y,v)=>{var E=cc(),T=ae(E);Ho(T,{get size(){return r()},insetContent:!0,includeBackground:!1,children:(k,w)=>{var x=uc(),A=ae(x);X(A,e,"default",{},null),Wn(x,te=>{var ne;return(ne=f.registerRoot)==null?void 0:ne.call(f,te)}),at(()=>ot(x,1,`l-dropdown-menu-augment__contents l-dropdown-menu-augment__contents--size-${r()}`,"svelte-o54ind")),O(k,x)},$$slots:{default:!0}}),O(y,E)},$$slots:{default:!0}}),ge(),s()}var dc=J('<div class="c-dropdown-menu-augment__item-icon svelte-48oly1"><!></div>'),mc=J('<div class="c-dropdown-menu-augment__item-icon svelte-48oly1"><!></div>'),hc=J("<!> <!> <!>",1);function os(t,e){const n=sa(e),s=B(e,["children","$$slots","$$events","$$legacy"]),r=B(s,["highlight","disabled","color","onSelect"]);he(e,!1);const[a,o]=Me(),i=()=>Ee(v,"$sizeState",a),l=Ie(),u=Ie(),d=Ie();let m=N(e,"highlight",24,()=>{}),p=N(e,"disabled",24,()=>{}),h=N(e,"color",24,()=>{}),f=N(e,"onSelect",8,()=>{});const _=Q(st),y=Q(ie.CONTEXT_KEY),v=_.size;function E(A){var S;if(p())return;const te=(S=y.rootElement)==null?void 0:S.querySelectorAll(`.${ie.ITEM_CLASS}`);if(!te)return;const ne=Array.from(te).findIndex(Ne=>Ne===A);ne!==-1&&y.setFocusedIdx(ne)}Se(()=>(F(l),F(u),Pe(r)),()=>{we(l,r.class),we(u,aa(r,["class"]))}),Se(()=>(Pe(p()),Pe(m()),F(l)),()=>{we(d,[p()?"":ie.ITEM_CLASS,"c-dropdown-menu-augment__item",m()?"c-dropdown-menu-augment__item--highlighted":"",F(l)].join(" "))}),rt(),pe();const T=Zt(()=>h()??"neutral"),k=Zt(()=>!h());var w=Ms(()=>Os("dropdown-menu-item","highlighted",m())),x=Ms(()=>Os("dropdown-menu-item","disabled",p()));So(t,Ve({get class(){return F(d)},get size(){return i()},variant:"ghost",get color(){return F(T)},get highContrast(){return F(k)},alignment:"left",get disabled(){return p()}},()=>F(w),()=>F(x),()=>F(u),{$$events:{click:A=>{A.currentTarget instanceof HTMLElement&&E(A.currentTarget),f()(A)},mouseover:A=>{A.currentTarget instanceof HTMLElement&&E(A.currentTarget)},mousedown:A=>{A.preventDefault(),A.stopPropagation()}},children:(A,te)=>{var ne=hc(),S=Te(ne),Ne=P=>{var de=dc(),Re=ae(de);X(Re,e,"iconLeft",{},null),O(P,de)};yt(S,P=>{je(()=>n.iconLeft)&&P(Ne)});var ze=_t(S,2);ra(ze,{get size(){return i()},children:(P,de)=>{var Re=Oe(),xt=Te(Re);X(xt,e,"default",{},null),O(P,Re)},$$slots:{default:!0}});var R=_t(ze,2),ce=P=>{var de=mc(),Re=ae(de);X(Re,e,"iconRight",{},null),O(P,de)};yt(R,P=>{je(()=>n.iconRight)&&P(ce)}),O(A,ne)},$$slots:{default:!0}})),ge(),o()}var pc=ta("<svg><!></svg>");function gc(t,e){const n=B(e,["children","$$slots","$$events","$$legacy"]);var s=pc();oa(s,()=>({xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16","data-ds-icon":"fa",viewBox:"0 0 16 16",...n}));var r=ae(s);Io(r,()=>'<path fill-opacity=".01" d="M0 0h16v16H0z"/><path fill-opacity=".365" d="M10.149 7.602a.56.56 0 0 1 0 .794l-3.5 3.502a.562.562 0 0 1-.795-.795L8.956 8 5.852 4.898a.562.562 0 0 1 .795-.795z"/>',!0),O(t,s)}function Zr(t,e){const n=B(e,["children","$$slots","$$events","$$legacy"]),s=B(n,[]);os(t,Ve({class:"c-dropdown-menu-augment__breadcrumb-chevron"},()=>s,{children:(r,a)=>{var o=Oe(),i=Te(o);X(i,e,"default",{},null),O(r,o)},$$slots:{default:!0,iconRight:(r,a)=>{gc(r,{slot:"iconRight"})}}}))}var fc=J("<div><!></div>");function Qr(t,e){const n=B(e,["children","$$slots","$$events","$$legacy"]),s=B(n,["requestOpen","requestClose","focusIdx","setFocusedIdx","getCurrentFocusedIdx","focusedIndex","defaultOpen","open","onOpenChange","delayDurationMs","nested","onHoverStart","onHoverEnd","triggerOn"]);he(e,!1);let r=N(e,"defaultOpen",24,()=>{}),a=N(e,"open",24,()=>{}),o=N(e,"onOpenChange",24,()=>{}),i=N(e,"delayDurationMs",24,()=>{}),l=N(e,"nested",24,()=>{}),u=N(e,"onHoverStart",8,()=>{}),d=N(e,"onHoverEnd",8,()=>{}),m=N(e,"triggerOn",24,()=>[Gt.Click]),p=Ie();const h=()=>{var w;return(w=F(p))==null?void 0:w.requestOpen()},f=()=>{var w;return(w=F(p))==null?void 0:w.requestClose()},_=w=>T.focusIdx(w),y=w=>T.setFocusedIdx(w),v=()=>T.getCurrentFocusedIdx(),E=Q(ie.CONTEXT_KEY),T=new ie(E);na(ie.CONTEXT_KEY,T);const k=T.focusedIndex;return pe(),ia(Go(t,Ve({get defaultOpen(){return r()},get open(){return a()},get onOpenChange(){return o()},get delayDurationMs(){return i()},onHoverStart:u(),onHoverEnd:d(),get triggerOn(){return m()},get nested(){return l()}},()=>s,{children:(w,x)=>{var A=Oe(),te=Te(A);X(te,e,"default",{},null),O(w,A)},$$slots:{default:!0},$$legacy:!0})),w=>we(p,w),()=>F(p)),Xe(e,"requestOpen",h),Xe(e,"requestClose",f),Xe(e,"focusIdx",_),Xe(e,"setFocusedIdx",y),Xe(e,"getCurrentFocusedIdx",v),Xe(e,"focusedIndex",k),ge({requestOpen:h,requestClose:f,focusIdx:_,setFocusedIdx:y,getCurrentFocusedIdx:v,focusedIndex:k})}var yc=J("<div></div>");function _c(t,e){let n=N(e,"size",8,1),s=N(e,"orientation",8,"horizontal"),r=N(e,"useCurrentColor",8,!1),a=N(e,"class",8,"");var o=yc();let i;at(l=>i=ot(o,1,`c-separator c-separator--size-${n()===.5?"0_5":n()} c-separator--orientation-${s()} ${a()}`,"svelte-o0csoy",i,l),[()=>({"c-separator--current-color":r()})],Zt),O(t,o)}var bc=J("<div><!></div>"),vc=J('<label class="c-text-field-label svelte-vuqlvc"><!></label>'),Ec=J('<div class="c-text-field__slot c-base-text-input__slot"><!></div>'),Tc=J('<div class="c-text-field__slot c-base-text-input__slot"><!></div>'),Sc=J("<!> <input/> <!>",1),Ic=J("<div><!> <!></div>");function wc(t,e){const n=sa(e),s=B(e,["children","$$slots","$$events","$$legacy"]),r=B(s,["variant","size","color","textInput","value","id"]);he(e,!1);const a=Ie(),o=Ie(),i=Ie(),l=Do();let u=N(e,"variant",8,"surface"),d=N(e,"size",8,2),m=N(e,"color",24,()=>{}),p=N(e,"textInput",28,()=>{}),h=N(e,"value",12,""),f=N(e,"id",24,()=>{});const _=`text-field-${Math.random().toString(36).substring(2,11)}`;function y(w){l("change",w)}Se(()=>Pe(f()),()=>{we(a,f()||_)}),Se(()=>(F(o),F(i),Pe(r)),()=>{we(o,r.class),we(i,aa(r,["class"]))}),rt(),pe();var v=Ic();ot(v,1,"c-text-field svelte-vuqlvc",null,{},{"c-text-field--has-left-icon":n.iconLeft!==void 0,"c-text-field--has-right-icon":n.iconRight!==void 0});var E=ae(v),T=w=>{var x=vc(),A=ae(x);X(A,e,"label",{},null),at(()=>Fo(x,"for",F(a))),O(w,x)};yt(E,w=>{je(()=>n.label)&&w(T)});var k=_t(E,2);Bo(k,{get variant(){return u()},get size(){return d()},get color(){return m()},children:(w,x)=>{var A=Sc(),te=Te(A),ne=R=>{var ce=Ec(),P=ae(ce);X(P,e,"iconLeft",{},null),O(R,ce)};yt(te,R=>{je(()=>n.iconLeft)&&R(ne)});var S=_t(te,2);oa(S,()=>({spellCheck:"false",class:`c-text-field__input c-base-text-input__input ${F(o)}`,id:F(a),...F(i)}),void 0,"svelte-vuqlvc"),ia(S,R=>p(R),()=>p());var Ne=_t(S,2),ze=R=>{var ce=Tc(),P=ae(ce);X(P,e,"iconRight",{},null),O(R,ce)};yt(Ne,R=>{je(()=>n.iconRight)&&R(ze)}),Vo(S,h),_e("change",S,y),_e("click",S,function(R){Ce.call(this,e,R)}),_e("keydown",S,function(R){Ce.call(this,e,R)}),_e("input",S,function(R){Ce.call(this,e,R)}),_e("blur",S,function(R){Ce.call(this,e,R)}),_e("dblclick",S,function(R){Ce.call(this,e,R)}),_e("focus",S,function(R){Ce.call(this,e,R)}),_e("mouseup",S,function(R){Ce.call(this,e,R)}),_e("selectionchange",S,function(R){Ce.call(this,e,R)}),O(w,A)},$$slots:{default:!0}}),O(t,v),ge()}var Nc=J("<div><!></div>"),Rc=J("<div><!></div>");const Cd={BreadcrumbBackItem:function(t,e){const n=B(e,["children","$$slots","$$events","$$legacy"]),s=B(n,[]);os(t,Ve({class:"c-dropdown-menu-augment__breadcrumb-back-chevron"},()=>s,{children:(r,a)=>{var o=Oe(),i=Te(o);X(i,e,"default",{},null),O(r,o)},$$slots:{default:!0,iconLeft:(r,a)=>{lc(r)}}}))},BreadcrumbItem:Zr,Content:Jr,Item:os,Label:function(t,e){he(e,!1);const[n,s]=Me(),r=()=>Ee(o,"$sizeState",n),a=Ie(),o=Q(st).size;Se(()=>r(),()=>{we(a,["c-dropdown-menu-augment__label-item",`c-dropdown-menu-augment__label-item--size-${r()}`].join(" "))}),rt(),pe();var i=fc(),l=ae(i);ra(l,{get size(){return r()},weight:"regular",children:(u,d)=>{var m=Oe(),p=Te(m);X(p,e,"default",{},null),O(u,m)},$$slots:{default:!0}}),at(()=>ot(i,1,Ds(F(a)),"svelte-gehsvg")),O(t,i),ge(),s()},Root:Qr,Separator:function(t,e){he(e,!1);const[n,s]=Me(),r=Q(st).size;pe();var a=bc();_c(ae(a),{size:4,orientation:"horizontal"}),at(()=>ot(a,1,`c-dropdown-menu-augment__separator c-dropdown-menu-augment__separator--size-${Ee(r,"$sizeState",n)}`,"svelte-24h9u")),O(t,a),ge(),s()},Sub:function(t,e){const n=B(e,["children","$$slots","$$events","$$legacy"]),s=B(n,[]);he(e,!1),pe();const r=Zt(()=>(Pe(Gt),je(()=>[Gt.Click,Gt.Hover])));Qr(t,Ve({nested:!0,get triggerOn(){return F(r)}},()=>s,{children:(a,o)=>{var i=Oe(),l=Te(i);X(l,e,"default",{},null),O(a,i)},$$slots:{default:!0}})),ge()},SubContent:function(t,e){const n=B(e,["children","$$slots","$$events","$$legacy"]),s=B(n,[]);he(e,!1);const[r,a]=Me(),o=()=>Ee(d,"$didOpen",r),i=Q(st).size,l=Q(ie.CONTEXT_KEY),u=Q(Ht.CONTEXT_KEY),d=Oo(u.state,m=>m.open);Se(()=>(o(),Kn),()=>{o()&&Kn().then(()=>l==null?void 0:l.focusIdx(0))}),Se(()=>o(),()=>{!o()&&(l==null||l.popNestedFocus())}),rt(),pe(),Jr(t,Ve(()=>s,{side:"right",align:"start",get size(){return Ee(i,"$sizeState",r)},children:(m,p)=>{var h=Oe(),f=Te(h);X(f,e,"default",{},null),O(m,h)},$$slots:{default:!0}})),ge(),a()},SubTrigger:function(t,e){he(e,!1);const[n,s]=Me(),r=Q(Ht.CONTEXT_KEY).state;pe(),Fs(t,{children:(a,o)=>{Zr(a,{get highlight(){return Ee(r,"$stateStore",n).open},children:(i,l)=>{var u=Oe(),d=Te(u);X(d,e,"default",{},null),O(i,u)},$$slots:{default:!0}})},$$slots:{default:!0}}),ge(),s()},TextFieldItem:function(t,e){const n=B(e,["children","$$slots","$$events","$$legacy"]),s=B(n,["value"]);he(e,!1);const[r,a]=Me(),o=()=>Ee(u,"$sizeState",r),i=Ie();let l=N(e,"value",12,"");const u=Q(st).size;Se(()=>o(),()=>{we(i,["c-dropdown-menu-augment__text-field-item",`c-dropdown-menu-augment__text-field-item--size-${o()}`].join(" "))}),rt(),pe();var d=Nc();wc(ae(d),Ve({get class(){return Pe(ie),je(()=>ie.ITEM_CLASS)},get size(){return o()}},()=>s,{get value(){return l()},set value(m){l(m)},$$legacy:!0})),at(()=>ot(d,1,Ds(F(i)),"svelte-1xu00bc")),O(t,d),ge(),a()},Trigger:function(t,e){he(e,!1);const[n,s]=Me(),r=()=>Ee(l,"$openState",n);let a=N(e,"referenceClientRect",24,()=>{});const o=Q(ie.CONTEXT_KEY),i=Q(Ht.CONTEXT_KEY),l=i.state;pe(),Fs(t,{get referenceClientRect(){return a()},$$events:{keydown:async u=>{switch(u.key){case"ArrowUp":u.preventDefault(),u.stopPropagation(),r().open||await o.clickFocusedItem(),o==null||o.focusIdx(-1);break;case"ArrowDown":u.preventDefault(),u.stopPropagation(),r().open||await o.clickFocusedItem(),o==null||o.focusIdx(0);break;case"Enter":u.preventDefault(),u.stopPropagation(),o==null||o.clickFocusedItem()}}},children:(u,d)=>{var m=Rc(),p=ae(m);X(p,e,"default",{},null),Wn(m,h=>{var f;return(f=o.registerTrigger)==null?void 0:f.call(o,h)}),Wn(m,h=>{var f;return(f=i.registerTrigger)==null?void 0:f.call(i,h)}),O(u,m)},$$slots:{default:!0}}),ge(),s()}};export{jc as $,Dn as A,Il as B,Nd as C,Cd as D,Td as E,Ji as F,Gc as G,Su as H,Wc as I,wl as J,no as K,Rl as L,Zi as M,Pl as N,kl as O,Al as P,xl as Q,Rd as R,z as S,wc as T,os as U,zc as V,Kc as W,qc as X,Ja as Y,Vc as Z,Yc as _,Ye as a,fl as a$,Hc as a0,Nl as a1,Ml as a2,gc as a3,Qi as a4,id as a5,ul as a6,ac as a7,Zc as a8,Ge as a9,hl as aA,oe as aB,cd as aC,pd as aD,Xt as aE,Kr as aF,lc as aG,ml as aH,dl as aI,nt as aJ,cl as aK,Jc as aL,eo as aM,rl as aN,Qc as aO,vu as aP,ho as aQ,nd as aR,td as aS,Cs as aT,Eu as aU,Tu as aV,rn as aW,Ul as aX,to as aY,rd as aZ,sd as a_,Qa as aa,nl as ab,Ll as ac,tl as ad,jr as ae,Fl as af,Dl as ag,Xc as ah,ed as ai,ud as aj,wd as ak,il as al,ll as am,Lc as an,Bc as ao,_c as ap,sn as aq,od as ar,Fu as as,ld as at,Uu as au,Et as av,md as aw,hd as ax,dd as ay,pl as az,al as b,gl as b0,yd as b1,Ed as b2,bd as b3,_d as b4,vd as b5,fd as b6,gd as b7,I as c,M as d,ol as e,sl as f,Qe as g,gt as h,q as i,L as j,tt as k,Du as l,se as m,Hu as n,Id as o,Sd as p,Vn as q,mt as r,ad as s,_l as t,bl as u,vl as v,El as w,$c as x,Tl as y,Sl as z};
