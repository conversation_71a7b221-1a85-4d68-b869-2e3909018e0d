.c-base-text-input.svelte-1mx5zy6{--base-text-field-border-width: 1px;--base-text-field-border-radius: var(--ds-radius-2);--base-text-field-height: var(--ds-spacing-5);--base-text-field-input-padding: var(--ds-spacing-2);--base-text-field-selection-color: var(--ds-color-a5);--base-text-field-focus-color: var(--ds-color-8);--base-text-field-text-color: var(--gray-12);--base-text-field-placeholder-color: var(--gray-a10);--base-text-field-slot-color: var(--gray-a11);--base-text-field-slot-padding: var(--ds-spacing-1);display:flex;align-items:stretch;height:var(--base-text-field-height);border-radius:var(--base-text-field-border-radius);box-shadow:var(--base-text-field-box-shadow);background-color:var(--base-text-field-bg-color);color:var(--base-text-field-text-color)}.c-base-text-input.svelte-1mx5zy6:has(.c-base-text-input__input:where(:disabled,:read-only)){--base-text-field-selection-color: var(--gray-a5);--base-text-field-focus-color: var(--gray-8);--base-text-field-text-color: var(--gray-a11);--base-text-field-placeholder-opacity: .5;--base-text-field-placeholder-color: var(--gray-a11);--base-text-field-placeholder-cursor: not-allowed}.c-base-text-input.svelte-1mx5zy6:focus-within{outline:2px solid var(--base-text-field-focus-color);outline-offset:-1px}.c-base-text-input.svelte-1mx5zy6 .c-base-text-input__slot{color:var(--base-text-field-slot-color);flex-shrink:0;display:flex;align-items:center;cursor:text;padding-inline:var(--base-text-field-slot-padding)}.c-base-text-input.svelte-1mx5zy6 .c-base-text-input__input{all:unset;width:100%;cursor:text;border-radius:calc(var(--base-text-field-border-radius) - var(--base-text-field-border-width));text-indent:var(--base-text-field-input-padding)}.c-base-text-input.svelte-1mx5zy6 .c-base-text-input__input::placeholder{color:var(--base-text-field-placeholder-color);opacity:var(--base-text-field-placeholder-opacity)}.c-base-text-input.svelte-1mx5zy6 .c-base-text-input__input:where(:placeholder-shown){cursor:var(--base-text-field-placeholder-cursor)}.c-base-text-input.svelte-1mx5zy6 .c-base-text-input__input::selection{background-color:var(--base-text-field-selection-color)}.c-base-text-input--has-color.svelte-1mx5zy6{--base-text-field-slot-color: var(--ds-color-a11)}.c-base-text-input--surface.svelte-1mx5zy6,.c-base-text-input--classic.svelte-1mx5zy6{--base-text-field-bg-color: var(--ds-surface);background-clip:content-box}.c-base-text-input--surface.svelte-1mx5zy6:has(.c-base-text-input__input:where(:autofill)):not(:disabled,:read-only),.c-base-text-input--classic.svelte-1mx5zy6:has(.c-base-text-input__input:where(:autofill)):not(:disabled,:read-only){--base-text-field-box-shadow: inset 0 0 0 1px var(--ds-color-a5), inset 0 0 0 1px var(--ds-color-a5);background-image:linear-gradient(var(--ds-color-a2),var(--ds-color-a2))}.c-base-text-input--surface.svelte-1mx5zy6:has(.c-base-text-input__input:where(:disabled,:read-only)),.c-base-text-input--classic.svelte-1mx5zy6:has(.c-base-text-input__input:where(:disabled,:read-only)){background-image:linear-gradient(var(--gray-a2),var(--gray-a2))}.c-base-text-input--surface.svelte-1mx5zy6{--base-text-field-box-shadow: inset 0 0 0 var(--base-text-field-border-width) var(--gray-a7)}.c-base-text-input--surface.svelte-1mx5zy6:has(.c-base-text-input__input:where(:disabled,:read-only)){--base-text-field-box-shadow: inset 0 0 0 var(--base-text-field-border-width) var(--gray-a6)}.c-base-text-input--classic.svelte-1mx5zy6{--base-text-field-box-shadow: inset 0 0 0 1px var(--gray-a5), inset 0 1.5px 2px 0 var(--gray-a2), inset 0 1.5px 2px 0 var(--black-a2)}.c-base-text-input--soft.svelte-1mx5zy6{--base-text-field-bg-color: var(--ds-color-a3);--base-text-field-text-color: var(--ds-color-12);--base-text-field-placeholder-color: var(--ds-color-12);--base-text-field-placeholder-opacity: .6;--base-text-field-border-width: 0px;--base-text-field-slot-color: var(--ds-color-12)}.c-base-text-input--soft.svelte-1mx5zy6.c-base-text-input--has-color{--base-text-field-slot-color: var(--ds-color-a11)}.c-base-text-input--soft.svelte-1mx5zy6:has(.c-base-text-input__input:where(:autofill)):not(:disabled,:read-only){--base-text-field-box-shadow: inset 0 0 0 1px var(--ds-color-a5), inset 0 0 0 1px var(--gray-a4)}.c-base-text-input--soft.svelte-1mx5zy6:has(.c-base-text-input__input:where(:disabled,:read-only)){--base-text-field-bg-color: var(--gray-a3)}.c-base-text-input--size-0_5.svelte-1mx5zy6{--base-text-field-height: var(--ds-spacing-4_5);--base-text-field-slot-padding: var(--ds-spacing-1)}.c-base-text-input--size-2.svelte-1mx5zy6{--base-text-field-height: var(--ds-spacing-6);--base-text-field-slot-padding: var(--ds-spacing-2)}.c-base-text-input--size-3.svelte-1mx5zy6{--base-text-field-border-radius: var(--ds-radius-3);--base-text-field-height: var(--ds-spacing-7);--base-text-field-input-padding: var(--ds-spacing-2);--base-text-field-slot-padding: var(--ds-spacing-3)}.c-base-text-input__input.svelte-1mx5zy6:where([type=number]:where(.svelte-1mx5zy6)){-moz-appearance:textfield;-webkit-appearance:textfield;appearance:textfield}.c-base-text-input__input.svelte-1mx5zy6::-webkit-inner-spin-button{-webkit-appearance:none}
