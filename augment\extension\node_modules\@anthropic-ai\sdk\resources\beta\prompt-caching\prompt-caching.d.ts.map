{"version": 3, "file": "prompt-caching.d.ts", "sourceRoot": "", "sources": ["../../../src/resources/beta/prompt-caching/prompt-caching.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAChD,OAAO,KAAK,WAAW,MAAM,YAAY,CAAC;AAE1C,qBAAa,aAAc,SAAQ,WAAW;IAC5C,QAAQ,EAAE,WAAW,CAAC,QAAQ,CAA0C;CACzE;AAED,yBAAiB,aAAa,CAAC;IAC7B,MAAM,QAAQ,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;IAC9C,MAAM,QAAQ,sCAAsC,GAAG,WAAW,CAAC,sCAAsC,CAAC;IAC1G,MAAM,QAAQ,gCAAgC,GAAG,WAAW,CAAC,gCAAgC,CAAC;IAC9F,MAAM,QAAQ,wBAAwB,GAAG,WAAW,CAAC,wBAAwB,CAAC;IAC9E,MAAM,QAAQ,6BAA6B,GAAG,WAAW,CAAC,6BAA6B,CAAC;IACxF,MAAM,QAAQ,+BAA+B,GAAG,WAAW,CAAC,+BAA+B,CAAC;IAC5F,MAAM,QAAQ,qBAAqB,GAAG,WAAW,CAAC,qBAAqB,CAAC;IACxE,MAAM,QAAQ,qCAAqC,GAAG,WAAW,CAAC,qCAAqC,CAAC;IACxG,MAAM,QAAQ,kCAAkC,GAAG,WAAW,CAAC,kCAAkC,CAAC;IAClG,MAAM,QAAQ,sBAAsB,GAAG,WAAW,CAAC,sBAAsB,CAAC;IAC1E,MAAM,QAAQ,qCAAqC,GAAG,WAAW,CAAC,qCAAqC,CAAC;IACxG,MAAM,QAAQ,sCAAsC,GAAG,WAAW,CAAC,sCAAsC,CAAC;IAC1G,MAAM,QAAQ,mBAAmB,GAAG,WAAW,CAAC,mBAAmB,CAAC;IACpE,MAAM,QAAQ,+BAA+B,GAAG,WAAW,CAAC,+BAA+B,CAAC;IAC5F,MAAM,QAAQ,4BAA4B,GAAG,WAAW,CAAC,4BAA4B,CAAC;CACvF"}